<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vote Now: Crown the Top Free MP4 to MKV Converter!</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .title {
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .subtitle {
            text-align: center;
            font-size: 16px;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.5;
        }

        .voting-section {
            display: block;
        }

        .voting-section.hidden {
            display: none;
        }

        .options-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .option-item {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .option-item:hover {
            border-color: #00d4aa;
            background-color: #f8fffe;
        }

        .option-item.selected {
            border-color: #00d4aa;
            background-color: #e6fffe;
        }

        .option-item input[type="checkbox"] {
            margin-right: 12px;
            transform: scale(1.2);
        }

        .option-label {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }

        .current-leader {
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            position: absolute;
            top: -8px;
            right: 10px;
        }

        .others-section {
            grid-column: 1 / -1;
            border: 2px solid #00d4aa;
            background-color: #e6fffe;
        }

        .others-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            margin-top: 10px;
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(45deg, #00d4aa, #00b894);
            color: white;
            border: none;
            padding: 16px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 212, 170, 0.3);
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .results-section {
            display: none;
        }

        .results-section.show {
            display: block;
        }

        .thanks-message {
            background: linear-gradient(45deg, #00d4aa, #00b894);
            color: white;
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .result-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .result-item.winner {
            background: linear-gradient(45deg, #00d4aa, #00b894);
            color: white;
        }

        .result-bar {
            flex: 1;
            height: 40px;
            background: #e0e0e0;
            border-radius: 20px;
            margin: 0 15px;
            position: relative;
            overflow: hidden;
        }

        .result-fill {
            height: 100%;
            background: linear-gradient(45deg, #00d4aa, #00b894);
            border-radius: 20px;
            transition: width 1s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .result-item.winner .result-fill {
            background: rgba(255, 255, 255, 0.3);
        }

        .result-votes {
            min-width: 80px;
            text-align: right;
            font-weight: bold;
        }

        .download-btn {
            width: 100%;
            background: linear-gradient(45deg, #00d4aa, #00b894);
            color: white;
            border: none;
            padding: 16px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 30px;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 212, 170, 0.3);
        }

        .error-message {
            color: #ff4757;
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">Vote Now: Crown the Top Free MP4 to MKV Converter! 🤔</h1>
        
        <div id="votingSection" class="voting-section">
            <p class="subtitle">We tested them all – now you decide! Which converter deserves the #1 spot? Select your top choice below:</p>
            
            <div class="options-grid">
                <div class="option-item" onclick="selectOption(this, 'filmora', event)">
                    <input type="checkbox" id="filmora" name="converter" value="filmora" onchange="handleCheckboxChange(this, this.parentElement, 'filmora')">
                    <label for="filmora" class="option-label">Filmora</label>
                    <span class="current-leader">CURRENT LEADER</span>
                </div>

                <div class="option-item" onclick="selectOption(this, 'cloudconvert', event)">
                    <input type="checkbox" id="cloudconvert" name="converter" value="cloudconvert" onchange="handleCheckboxChange(this, this.parentElement, 'cloudconvert')">
                    <label for="cloudconvert" class="option-label">CloudConvert</label>
                </div>

                <div class="option-item" onclick="selectOption(this, 'inverse-ai', event)">
                    <input type="checkbox" id="inverse-ai" name="converter" value="inverse-ai" onchange="handleCheckboxChange(this, this.parentElement, 'inverse-ai')">
                    <label for="inverse-ai" class="option-label">Inverse AI Video Converter</label>
                </div>

                <div class="option-item" onclick="selectOption(this, 'freeconvert', event)">
                    <input type="checkbox" id="freeconvert" name="converter" value="freeconvert" onchange="handleCheckboxChange(this, this.parentElement, 'freeconvert')">
                    <label for="freeconvert" class="option-label">FreeConvert</label>
                </div>

                <div class="option-item" onclick="selectOption(this, 'cloudconvert2', event)">
                    <input type="checkbox" id="cloudconvert2" name="converter" value="cloudconvert2" onchange="handleCheckboxChange(this, this.parentElement, 'cloudconvert2')">
                    <label for="cloudconvert2" class="option-label">CloudConvert</label>
                </div>

                <div class="option-item" onclick="selectOption(this, 'inverse-ai2', event)">
                    <input type="checkbox" id="inverse-ai2" name="converter" value="inverse-ai2" onchange="handleCheckboxChange(this, this.parentElement, 'inverse-ai2')">
                    <label for="inverse-ai2" class="option-label">Inverse AI Video Converter</label>
                </div>

                <div class="option-item" onclick="selectOption(this, 'freeconvert2', event)">
                    <input type="checkbox" id="freeconvert2" name="converter" value="freeconvert2" onchange="handleCheckboxChange(this, this.parentElement, 'freeconvert2')">
                    <label for="freeconvert2" class="option-label">FreeConvert</label>
                </div>

                <div class="option-item others-section" onclick="selectOption(this, 'others', event)">
                    <input type="checkbox" id="others" name="converter" value="others" onchange="handleCheckboxChange(this, this.parentElement, 'others')">
                    <label for="others" class="option-label">Others (please specify below)</label>
                    <input type="text" class="others-input" id="othersInput" placeholder="Enter your converter name" onclick="event.stopPropagation()">
                </div>
            </div>
            
            <button class="submit-btn" onclick="submitVote()" id="submitBtn" disabled>Submit Your Vote</button>
            <div id="errorMessage" class="error-message"></div>
        </div>
        
        <div id="resultsSection" class="results-section">
            <div class="thanks-message">
                <h2>👏Thanks for voting! Your input helps others make smarter choices.🔥</h2>
                <p>See how your favorite converter stacks up below!</p>
            </div>
            
            <div id="resultsContainer">
                <!-- Results will be populated by JavaScript -->
            </div>
            
            <button class="download-btn" onclick="downloadFilmora()">
                💻 Download Filmora
            </button>
        </div>
    </div>

    <script>
        // 页面特定ID，用于localStorage
        const PAGE_ID = 'mp4-to-mkv-converter-vote';
        const STORAGE_KEY = `vote_${PAGE_ID}`;
        
        // 预设投票数据
        const voteData = {
            'filmora': 134,
            'cloudconvert': 77,
            'inverse-ai': 40,
            'freeconvert': 22,
            'cloudconvert2': 2,
            'inverse-ai2': 0,
            'freeconvert2': 0,
            'others': 0
        };
        
        // 选项显示名称
        const optionNames = {
            'filmora': 'Filmora',
            'cloudconvert': 'CloudConvert',
            'inverse-ai': 'Inverse AI Video Converter',
            'freeconvert': 'FreeConvert',
            'cloudconvert2': 'CloudConvert',
            'inverse-ai2': 'Inverse AI Video Converter',
            'freeconvert2': 'FreeConvert',
            'others': 'Others'
        };
        
        let selectedOptions = [];
        const totalOptions = Object.keys(voteData).length;
        const maxSelections = totalOptions > 5 ? 3 : 1;
        
        // 页面加载时检查localStorage
        window.onload = function() {
            const hasVoted = localStorage.getItem(STORAGE_KEY);
            if (hasVoted) {
                showResults();
            }
        };
        
        function selectOption(element, value, event) {
            // 如果点击的是输入框本身，让浏览器处理默认行为
            if (event && event.target.type === 'checkbox') {
                return;
            }

            const checkbox = element.querySelector('input[type="checkbox"]');

            if (checkbox.checked) {
                // 取消选择
                checkbox.checked = false;
                element.classList.remove('selected');
                selectedOptions = selectedOptions.filter(option => option !== value);
            } else {
                // 检查是否超过最大选择数
                if (selectedOptions.length >= maxSelections) {
                    showError(`最多只能选择 ${maxSelections} 项`);
                    return;
                }

                // 选择
                checkbox.checked = true;
                element.classList.add('selected');
                selectedOptions.push(value);
            }

            // 更新提交按钮状态
            updateSubmitButton();
            clearError();
        }

        // 处理checkbox直接点击事件
        function handleCheckboxChange(checkbox, element, value) {
            if (checkbox.checked) {
                // 检查是否超过最大选择数
                if (selectedOptions.length >= maxSelections) {
                    checkbox.checked = false;
                    showError(`最多只能选择 ${maxSelections} 项`);
                    return;
                }

                element.classList.add('selected');
                selectedOptions.push(value);
            } else {
                element.classList.remove('selected');
                selectedOptions = selectedOptions.filter(option => option !== value);
            }

            // 更新提交按钮状态
            updateSubmitButton();
            clearError();
        }
        
        function updateSubmitButton() {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = selectedOptions.length === 0;
        }
        
        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
        }
        
        function clearError() {
            document.getElementById('errorMessage').textContent = '';
        }
        
        function submitVote() {
            if (selectedOptions.length === 0) {
                showError('请至少选择一个选项');
                return;
            }
            
            // 更新投票数据
            selectedOptions.forEach(option => {
                voteData[option]++;
            });
            
            // 保存到localStorage
            localStorage.setItem(STORAGE_KEY, JSON.stringify({
                voted: true,
                selections: selectedOptions,
                timestamp: new Date().toISOString()
            }));
            
            // 显示结果
            showResults();
        }
        
        function showResults() {
            document.getElementById('votingSection').classList.add('hidden');
            document.getElementById('resultsSection').classList.add('show');
            
            // 计算总票数和百分比
            const totalVotes = Object.values(voteData).reduce((sum, votes) => sum + votes, 0);
            
            // 按票数排序
            const sortedResults = Object.entries(voteData)
                .map(([key, votes]) => ({
                    key,
                    name: optionNames[key],
                    votes,
                    percentage: totalVotes > 0 ? Math.round((votes / totalVotes) * 100) : 0
                }))
                .sort((a, b) => b.votes - a.votes);
            
            // 生成结果HTML
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = sortedResults.map((result, index) => `
                <div class="result-item ${index === 0 ? 'winner' : ''}">
                    <div style="min-width: 120px; font-weight: bold;">
                        ${result.name}
                    </div>
                    <div class="result-bar">
                        <div class="result-fill" style="width: ${result.percentage}%">
                            ${result.percentage}%
                        </div>
                    </div>
                    <div class="result-votes">
                        ${result.votes} votes
                    </div>
                </div>
            `).join('');
        }
        
        function downloadFilmora() {
            alert('跳转到Filmora下载页面...');
            // 这里可以添加实际的下载链接
            // window.open('https://filmora.wondershare.com/download/', '_blank');
        }
    </script>
</body>
</html>
