<!doctype html>
<html lang="en">
<title>Video Mask</title>
<%= require('html-loader!./commonHTML/head.html') %>
<link rel="stylesheet" href="https://neveragain.allstatics.com/2019/assets/vendor/swiper.min.css">
<style>
  .rounded-1 {border-radius: .25rem;}
  .rounded-2 {border-radius: .625rem;}
  .bg-gray {background-color: #F8F8F8!important;}
  .text-gray {color: #787878!important;}
  .text-gray-heavy {color: #636363!important;}
  a.text-gray:hover, a.text-gray-heavy:hover {color: #000000!important;}
  .text-underline {text-decoration: underline;}
  .btn-white {background-color: #FFFFFF!important;border: 1px solid #000000!important;padding: .78125rem 1.5rem;}
  .btn-lg.btn-white {padding: 1.09375rem 2.125rem;}
  .btn-white:hover, .btn-white:focus, .btn-white:active {background-color: #000000!important;color: #FFFFFF!important;}
  .embed-responsive .wsc-youtube-play {position: absolute;top: 0;left: 0;width: 100%;height: 100%;-webkit-transition: background-color .3s ease;-o-transition: background-color .3s ease;transition: background-color .3s ease;}
  .embed-responsive .wsc-youtube-play svg {position: absolute;top: 0;right: 0;bottom: 0;left: 0;height: 100%!important;margin: auto;-webkit-filter: drop-shadow(0 3px 6px rgba(0, 0, 0, .5));filter: drop-shadow(0 3px 6px rgba(0, 0, 0, .5));opacity: .8;-webkit-transition: opacity .3s ease;-o-transition: opacity .3s ease;transition: opacity .3s ease;fill: #FFFFFF;stroke: #FFFFFF!important;}
  .embed-responsive.wsc-youtube-inline:hover .wsc-youtube-play {background-color: rgba(0, 0, 0, .2);}
  .wsc-icon.wsc-logo[data-icon*="vertical"] {height: 8.75rem;}
  .wsc-icon.wsc-feature {height: 4rem;}
  .wsc-icon.wsc-feature svg {height: 100%;}

  .swiper-button-prev, .swiper-button-next {top: calc(50% + 6rem);transform: translateY(calc(50% - 7rem));width: 4rem;height: 4rem;background-image: none;margin-top: 0;}
  .swiper-button-prev {left: 1.5rem;}
  .swiper-button-next {right: 1.5rem;}

  .bg-hero {background-image: linear-gradient(275.7deg, #DCECFF 0%, #E2FFF8 76.61%);}
  .effects-swiper .swiper-wrapper {-webkit-transition-timing-function: linear;-moz-transition-timing-function: linear;-ms-transition-timing-function: linear;-o-transition-timing-function: linear;transition-timing-function: linear;}
  .effects-swiper .swiper-slide {width: auto;max-width: 25rem;}
  .features-swiper a, .features-swiper .wsc-icon svg {transition: all .3s;transform-origin: 50%  50%;}
  .features-swiper a:hover {font-weight: 700;}
  .features-swiper a:hover .wsc-icon svg {transform: scale(1.15);}
   
  [data-toggle=collapse] [data-icon=symbol-chevron-bottom],[data-toggle=collapse] [data-icon=symbol-plus]{-webkit-transition:all .3s;transition:all .3s;}
  [data-toggle=collapse][aria-expanded=true] [data-icon=symbol-chevron-bottom]{-webkit-transform:rotate(180deg);transform:rotate(180deg);}
  [data-toggle=collapse][aria-expanded=true] [data-icon=symbol-plus]{-webkit-transform:rotate(45deg);transform:rotate(45deg);}
  #ask-individuals .border{padding:2.25rem 2.5rem;border:1px solid #ddd!important;border-radius:10px;}

  @media (min-width: 1280px) {
    .rounded-xl-3 {border-radius: 1rem;}
  }
  @media (max-width: 1280px) {
    h1.display-2 {font-size: 2.75rem;}

    .swiper-button-prev, .swiper-button-next {top: calc(50% + 3rem);transform: translateY(calc(50% - 4.5rem));}
    .swiper-button-prev {left: -1.5rem;}
    .swiper-button-next {right: -1.5rem;}
  }
  @media (max-width: 992px) {
    h1.display-2 {font-size: 3.5rem;}
    
    h2.h1 {font-size: 2.25rem;}
    h3.h2 {font-size: 1.75rem;}
    .font-size-small {font-size: 14px;}
  }
  @media (max-width: 576px) {
    h1.display-2 {font-size: 3rem;}
    h2.display-3 {font-size: 32px;}
    h2.h1 {font-size: 2rem;}
    h3.h2 {font-size: 1.5rem;}
    .container, .container-fluid, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {padding-left: 20px;padding-right: 20px;}

    .features-swiper .swiper-slide {width: auto;}

    #ask-individuals .border{padding:1.4rem 1.5rem;}
  }
.breadCrumbs {color: #acbbbd;}
.breadCrumbs a, .breadCrumbs span{opacity: 1;color: #07273D;}
</style>




<style>
  /* 新样式 */
	main{font-family: "HarmonyOS Sans",-apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"}
	main .btn {border-radius: 0.5rem;text-transform: capitalize;}
	.mx-30 {margin-left: 1.875rem;margin-right: 1.875rem;}
	.my-30 {margin-top: 1.875rem;margin-bottom: 1.875rem;}
	.rounded-20 {border-radius: 1.25rem;}
  .rounded-16 {border-radius: 1rem;}
  .rounded-8 {border-radius: .5rem;}
  .opacity-6 {opacity: 0.6;}
  .opacity-7 {opacity: 0.7;}
  .opacity-8 {opacity: 0.8;}
  main .text-secondary {color: #50E3C2 !important;}
  .font-size-44 {font-size: 44px;}
  .line-height-150 {line-height: 1.5;letter-spacing: -0.02em;}
  main .btn-secondary {color: #000;background-color: #50e3c2;border-color: #50e3c2;}
  main .btn-secondary:hover {color: #000;background-color: #2fdeb7;border-color: #2fdeb7;}
  main .text-linear {background-image: linear-gradient(82.19deg, #55FFD6 4.37%, #11D4FF 87.06%);background-clip: text;-webkit-background-clip: text;color: transparent;display: inline-block;}
  .video-container {position: relative;overflow: hidden;background-size: cover;transform: scale(1);}
  .video-container video {position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%);width: 100%;height: 100%;object-fit: cover;}
  .video-container::before {content: '';display: block;padding-top: var(--ratio, calc(9 / 16 * 100%));}
  .img-container {position: relative;padding-top: var(--ratio, calc(9 / 16 * 100%));overflow: hidden;}
  .img-container img {position: absolute;top: 0;left: 0;width: 100%;height: 100%;object-fit: cover;}
	.mobile-qrcode-container {position: relative;display: inline-block;width: 4rem;flex-shrink: 0;}
  .mobile-qrcode-container .mobile-qrcode-wrapper {position: absolute;top: -8px;left: 50%;transform: translate(-50%,-100%);width:120px;border-radius: 6px;background-color: #fff;color: #000;text-align: center;opacity: 0;pointer-events: none;}
  .mobile-qrcode-container:hover .mobile-qrcode-wrapper{opacity: 1;}
  .mobile-qrcode-container .mobile-qrcode-wrapper::after{content:'';position:absolute;display:block;left:50%;bottom:0;transform: translate(-50%,100%);width: 0;height: 0;border-style: solid;border-width: 6px 6px 0 6px;border-color: #ffffff transparent transparent transparent;}
  .mobile-qrcode-container .mobile-qrcode-wrapper .qrcode-img {border-radius: inherit;overflow: hidden;}
  .mobile-qrcode-container .mobile-qrcode-wrapper .qrcode-desc {font-size: 12px;font-weight: 700;line-height: 1;color: #000;padding-bottom: 6px;}

  .part-banner .breadCrumbs {color: inherit;}

  .part-feature #feature-tab {display: flex;width: 830px;max-width: 100%;background-color: #2023280D;border-radius: 14px;padding: 10px;margin: 0 auto;justify-content: space-between;}
  .part-feature #feature-tab .nav-item {width: 49.18%;background: #fff;display: flex;align-items: center;height: 100%;text-align: left;padding: 14px 30px;color: rgba(0, 0, 0, .6);border-radius: 14px;}
  .part-feature #feature-tab .nav-item .icon {width: 2.5rem;margin-right: 22PX;flex-shrink: 0;}
  .part-feature #feature-tab .nav-item .title {font-size: 1.125rem;font-weight: 600;margin-bottom: 4px;color: #000;font-family: 'Outfit', sans-serif;}
  .part-feature #feature-tab .nav-item .desc {font-size: 14px;}
  .part-feature #feature-tab .nav-item.active {background: linear-gradient(93.12deg, #53FBD5 67.12%, #75FF9C 98.21%, #BDFF89 112.63%);color: #000;}
  .part-feature #feature-tab #nav-feature2-tab.active {background: linear-gradient(92.52deg, #00FFF0 -18.36%, #00C2FF 65.91%, #0075FF 112.92%);}
  .part-feature .feature-intro {background: #fff;border-radius: 30px;text-align: left;padding: 30px;color: #000;border: 1px solid #CCCCCC;}
	.part-feature .intro-box {display: flex;align-items: center;padding: 20px 34px 0;}
	.part-feature .intro-box h2 {font-size: 34px;letter-spacing: -0.02em;width: 39%;flex-shrink: 0;margin-right: 40px;}
  .part-feature .feature-intro .box-style {border-radius: 20px;padding: 30px 15px;height: 100%;color: #000;text-align: center;}
  .part-feature .feature-intro .box-style .icon {width: 48px;margin-bottom: 4px;display: inline-block;}
  .part-feature .feature-intro .box-style .title {font-size: 18px;font-weight: 700;font-family: 'Outfit';}
	.part-feature #nav-feature2 .feature-intro {padding-top: 10px;}
  .part-feature .desc-list {margin: 0;padding-left: 1.5rem;list-style: disc;}
  .part-feature .desc-list li {line-height: 1.5;color: #636363;font-size: 14px;}
  .part-feature .editing-box {background-color: #fff;border-radius: 30px;border: 1px solid #CCCCCC;padding: 30px;}
  .part-feature .editing-box .box-style {border-radius: 20px;padding: 40px 15px;height: 100%;color: #000;background:linear-gradient(115.78deg, rgba(83, 251, 213, 0.2) 73.96%, rgba(117, 255, 156, 0.2) 92.38%, rgba(189, 255, 137, 0.2) 106.94%);display: flex;justify-content: center;}
  .part-feature #nav-feature2 .editing-box .box-style {background: linear-gradient(110.04deg, rgba(0, 255, 240, 0.2) -38.57%, rgba(0, 194, 255, 0.2) 56.08%, rgba(0, 117, 255, 0.2) 102.64%);}
  .part-feature .editing-box .box-style .box-top {display: flex;align-items: center;margin-bottom: 20px;}
  .part-feature .editing-box .box-style .icon {width: 38px;flex-shrink:0;margin-right: 10px;}
  .part-feature .editing-box .box-style .title {font-size: 24px;font-weight: 700;font-family: 'Outfit';}
  .part-feature .editing-box .box-style ul {margin: 0;padding-left: 1.5rem;list-style: disc;line-height: 1.7;opacity: .7;}

  .part-steps {background: #DFEEFF;border-radius: 1.25rem;}
  .part-steps .swiper {overflow: hidden;}
  .part-steps .nav {width: 845px;max-width: 100%;margin: 0 auto; justify-content: space-between;}
  .part-steps .nav .nav-item {width: 49%;padding: 12px .75rem;color: rgba(0,0,0,.8);font-size: 1.125rem;background: #2023281A;border-radius: .5rem;font-weight: 700;text-align: center;}
  .part-steps .nav .nav-item.active {background-color: #07273D;color: #fff;}
  .part-steps .step-list {position: relative;list-style: none;margin: 0;padding: 0;text-align: left;}
  .part-steps .step-list::before {content: '';position: absolute;height: 100%;width: 2px;background-color: rgba(0, 0, 0, .3);border-radius: 0.5rem;top: 0;left: -27px;}
  .part-steps .step-list li { opacity: .5;border: 0;padding: 1rem 0;position: relative;cursor: pointer;}
  .part-steps .step-list li::before {content: '';position: absolute;width: 4px;height: 0;background-color: #000;top: 50%;left: -28px;transform: translateY(-50%);transition: height .2s linear;border-radius: 0.5rem;}
  .part-steps .step-list li:first-child {padding-top: 0;}
  .part-steps .step-list li:last-child {padding-bottom: 0;}
  .part-steps .step-list li h5 {letter-spacing: -0.02em;}
  .part-steps .step-list li h5+div {display: none;}
  .part-steps .step-list li.active {background-color: transparent;opacity: 1;border: 0;}
  .part-steps .step-list li.active::before {height: 100%;}
  .part-steps .step-list li.active h5+div {display: block;}
  .part-steps #swiper-step .swiper-pagination {line-height: 1;bottom: 0;}

  .part-table .table-wrapper {border-radius: 30px;overflow: hidden;color: #000;background-color: #000;position: relative;padding: 8px;}
	.part-table .table-wrapper .layer-item {position: absolute;width: 32.6%;height: calc(100% - 16px);background: #FFFFFF21;border-radius: 24px;}
	.part-table .table-wrapper .layer1 {top: 8px;left: 8px;}
	.part-table .table-wrapper .layer2 {top: 8px;left: 33.7%;background: linear-gradient(154.03deg, #53FBD5 72.48%, #75FF9C 87.16%, #BDFF89 98.78%);}
	.part-table .table-wrapper .layer3 {top: 8px;right: 8px;background: linear-gradient(55.79deg, #53FBD5 3.43%, #00C2FF 75.63%, #0075FF 115.9%);}
	.part-table table {position: relative;z-index: 1;text-align: left;}
	.part-table table td {padding: 1rem 90px;border-color: #FFFFFF4D;vertical-align: middle;line-height: 1.1;}
	.part-table .table thead th {width: 33.33%;border-top: none;border-color: #FFFFFF66;font-size: 20px;font-weight: 700;letter-spacing: -0.02em;font-family: 'Outfit';text-align: center;}
	.part-table table thead th:first-child{color: #fff;}
	.part-table table tbody tr td:first-child {text-align: center;color: rgba(255,255,255,.7);}
	.part-table table thead th,.part-table table td {background-color: transparent;}
	.part-table table .tag-text {display: inline-block;background-color: rgba(2, 2, 2, .5);color: #53FBD5;font-size: 14px;font-weight: 700;padding: 6px 16px;border-radius: 50rem;margin:0 2px;}
  .part-table table tbody tr td:last-child .tag-text {color: #39F5FF;}
	.part-table table .tag-text.no {color: #FF7070 !important;}
	.part-table table td div:first-child {display: flex;justify-content: center;font-size: 14px;align-items: center;}
	.part-table .tag-begin,.part-table .tag-profess {display: inline-block;background-color: #000;border-radius: 50rem;font-size: 14px;font-weight: 600;font-family: 'Outfit';padding: 2px 14px;text-align: center;margin-left: 8px;}
	.part-table .tag-begin .text-linear {background-image: linear-gradient(91.58deg, #53FBD5 76.95%, #75FF9C 89.79%, #BDFF89 99.95%);}
	.part-table .tag-profess .text-linear {background-image: linear-gradient(55.79deg, #53FBD5 3.43%, #00C2FF 75.63%, #0075FF 115.9%);border-radius: 30px;}


	.part-need .box-style {height: 100%;border-radius: 30px;background: #FFFFFF12;border: 1px solid #00000033;padding: 12px;display: flex;flex-direction: column;}
	.part-need .box-style .img-container {--radio: calc(210 / 426 * 100%);border-radius: 14px;}
	.part-need .box-style .img-container img {transition: transform cubic-bezier(.4,0,.2,1) .3s;}
	.part-need .box-style .box-content {padding: 1rem 1.125rem 1.125rem;text-align: left;display: flex;flex-direction: column;justify-content: space-between;flex: 1 1 auto;}
	.part-need .box-style h5 {margin-bottom: 12px;}
	.part-need .box-style .desc {font-size: 14px;line-height: 1.5;color: rgba(0, 0, 0, .7);}
  .part-need .box-style .tag-list {display: flex;flex-wrap: wrap;padding-top: 12px;align-items: center;margin:-2px}
  .part-need .box-style .tag-item {display: inline-block;background-color: #00000012;border-radius: 50rem;font-size: 12px;font-weight: 400;padding: 6px 12px;text-align: center;margin: 2px;}

  .part-blogs .box-style {height: 100%;border-radius: 30px;background: #DFEEFF;padding: 12px;}
	.part-blogs .box-style .img-container {--radio: calc(210 / 426 * 100%);border-radius: 14px;}
	.part-blogs .box-style .img-container img {transition: transform cubic-bezier(.4,0,.2,1) .3s;}
	.part-blogs .box-style .box-content {padding: 1rem 1.125rem 1.125rem;text-align: left;}
	.part-blogs .box-style h5 {margin-bottom: 12px;color: #000;}
	.part-blogs .box-style .desc {font-size: 14px;line-height: 1.5;color: #07273D;}

	.faq-option {border-bottom: 1px solid #E2E2E2;padding: 1rem 0 2rem;}
  .faq-option .faq-item {padding-top: 16px;padding-bottom: 16px;font-size: 24px;transition: all .3s;}
  .faq-option .faq-item.collapsed {padding-top: 16px;padding-bottom: 0;}
  .faq-option:hover .faq-item.collapsed {color: #50E3C2;}
  .faq-option .faq-item svg:nth-child(1) {display: none;}
  .faq-option .faq-item svg:nth-child(2) {display: inline-block;}
  .faq-option .faq-item.collapsed svg:nth-child(1) {display: inline-block;}
  .faq-option .faq-item.collapsed svg:nth-child(2) {display: none;}
	.part-faq .show-more {width: 240px;max-width: 100%;border-radius: .5rem;background-color: #E9E9E9;height: 50px;cursor: pointer;display: flex;align-items: center;justify-content: center;font-size: 1.125rem;margin: 0 auto;color: #000000CC;font-weight: 700;margin-top: 2rem;}
  .part-faq .show-more .less {display: none;}
  .part-faq .show-more svg {transition: all .3s;}
  .part-faq .show-more.show .more {display: none;}
  .part-faq .show-more.show .less {display: block;}
  .part-faq .show-more.show svg {transform: rotate(180deg);}

	.swiper-pagination {bottom: 0 !important;}
  .swiper-pagination-bullet {width: 8px;height: 8px;margin: 0 4px !important;opacity: .2;}
  .swiper-pagination-bullet-active { background-color: #07273D;opacity: 1;}

  .part-ai .box-style {position: relative;display: block;text-decoration: none;transition: all .3s ease-out;overflow: hidden;}
  .part-ai .box-style::before {content: '';position: absolute;width: 100%;height: 42%;left: 0;bottom: 0;background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, .5) 86.75%);}
  .part-ai .box-style .box-title {position: absolute;width: 100%;bottom: 0;left: 0;padding: 2rem;color: #fff;z-index: 2;}

  .part-bottom {background-color: #000;background-image: url('https://images.wondershare.com/filmora/features/video-denoiser/bottom-bg.jpg?ivm-quality=OD');background-size: cover;background-repeat: no-repeat;background-position: center;}
  .part-bottom h2 {background-image: linear-gradient(82.19deg, #55FFD6 4.37%, #11D4FF 87.06%);background-clip: text;-webkit-background-clip: text;color: transparent;display: inline-block;}
  main .btn-linear {background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 50%, #FFFFFF 100%), linear-gradient(93.19deg, #83FFE9 0%, #58FFDA 50.5%, #00F0FF 100%);background-color: #fff;background-size: 100% 128px;background-position: 0 0;border:none;transition: all .15s linear;background-repeat: no-repeat;color:#000;}
  main .btn-linear [data-icon="brand-windows"]::before, main .btn-linear [data-icon="brand-macos"]::before {content: '';width: 100%;height: 100%;background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTYuNjQzMVYyMC4wNzE2SDIwVjE2LjY0MzEiIHN0cm9rZT0iYmxhY2siIHN0cm9rZS13aWR0aD0iMi4yODU3MSIvPgo8cGF0aCBkPSJNMTIuMDAwMSAyLjkyODcxVjE0LjM1NzNNMTIuMDAwMSAxNC4zNTczTDE3LjcxNDMgOS43OTg4OE0xMi4wMDAxIDE0LjM1NzNMNi4yODU3NyA5Ljc5ODg4IiBzdHJva2U9ImJsYWNrIiBzdHJva2Utd2lkdGg9IjIuMjg1NzEiLz4KPC9zdmc+Cg==');background-size: 100% auto;background-position: center bottom;background-repeat: no-repeat;position: absolute;top:0;left: 0;z-index: 1;pointer-events: none;opacity: 0;}
  main .btn-linear.white:hover {background: #07273d;color: #fff;}

  .part-bottom .btn-linear {background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 50%, #FFFFFF 100%), linear-gradient(93.19deg, #83FFE9 0%, #58FFDA 50.5%, #00F0FF 100%);background-color: #fff;background-size: 100% 128px;background-position: 0 0;border:none;transition: all .15s linear;background-repeat: no-repeat;color:#000;}
  .part-bottom .btn-linear:hover {background-position: 0 -128px;background-color:#fff;color:#000;}

  @media (any-hover) {
    .part-ai .box-style:hover {transform: translateY(-8px);box-shadow: 4px 4px 24px 0px #0000001F;}
		.part-need .box-style:hover img,.part-blogs .box-style:hover img {transform: scale(1.1);}
  }

  @media (min-width: 1280px) {
    main .btn-linear.btn-lg{min-height: 64px;border: 0 !important;}
    main .btn-linear:hover {background-position: 0 -128px;background-color:#fff;color:#000;}
    .part-feature #feature-tab .nav-item:hover {background: linear-gradient(93.12deg, #53FBD5 67.12%, #75FF9C 98.21%, #BDFF89 112.63%);color: #000;}
    .part-feature #feature-tab #nav-feature2-tab:hover {background: linear-gradient(92.52deg, #00FFF0 -18.36%, #00C2FF 65.91%, #0075FF 112.92%);}
  }


  @media (max-width: 1440px) {
    .font-size-44,main .display-3 {font-size: 34px;}
    main .h2,main h1 {font-size: 1.5rem;}
    .part-feature .feature-intro .box-style .icon {width: 64px;}
		.part-table table td {padding: 1rem 40px;}
  }
  @media (max-width: 1280px) {
    h2,.font-size-44{font-size: 28px;}
		.mobile-qrcode-container {display: none;}
    .part-ai {margin: 0 !important;border-radius: 0;}
    .part-ai .box-style .box-title {padding: 1.5rem;}
    .part-steps .swiper-wrapper {flex-direction: column;}
    .part-steps .step-content {font-size: 16px;color: #444;line-height: 1.6;margin-top: 24px;margin-bottom: 8px;text-align: left;}
    .part-steps .step-content h5 {font-size: 16px;color: #000;line-height: 1.2;display: flex;}
    .part-steps .step-content .step-num {display: inline-block;background: #50E3C2;border-radius: 4px;margin-right: 8px;font-size: 14px;font-weight: 700;color: #000;padding: 3px 6px;margin-top: -2px;flex-shrink: 0;height: 100%;}
    .part-feature .feature-intro {padding: 1rem;}
    .part-feature #feature-tab .nav-item {padding: 14px 1rem;}
		.part-feature .intro-box {padding: 10px 10px 0;flex-direction: column;align-items: start;}
		.part-feature .intro-box h2 {font-size: 24px;width: auto;margin-right: 0;}
		.part-feature .feature-intro .box-style {padding: 24px;}
		.part-table table td {padding: 1rem 24px;}
		.part-need .box-style {border-radius: 15px;padding: 8px;}
		.part-need .box-style .img-container {border-radius: 12px;}
		.part-need .box-style .box-content {padding: 8px;}
		.part-need .box-style h5 {margin-bottom: 8px;}
  }
  @media (max-width: 992px) {
    main h1 {font-size: 24px;}
    .faq-option .faq-item {font-size: 18px;}
		.part-table .tag-begin, .part-table .tag-profess {margin-left: 0;}
		.part-feature .feature-intro {border-radius: 15px;}
		.part-feature .feature-intro .box-style {flex-direction: column;align-items: start;padding: 12px;border-radius: 15px;}
		.part-feature .feature-intro .box-style .icon {width: 40px;}
		.part-feature .feature-intro .box-style .title {font-size: 20px;}
    .part-feature .editing-box .box-style  {padding: 20px 15px;border-radius: 15px;}
    .part-feature .editing-box .box-style .box-top {flex-direction: column;margin-bottom: 8px;}
    .part-feature .editing-box .box-style .icon {margin-right: 0;margin-bottom: 4px;}
    .part-feature .editing-box .box-style .title {font-size: 20px;}
  }
  @media (max-width:576px) {
    .font-size-44 {font-size: 24px;}
    main .btn-lg {margin: 4px 0 !important;min-width: 80%;justify-content: center;}
    .mx-30 {padding-top: 15px;padding-bottom: 15px;}
    .my-30 {margin-left: 15px;margin-right: 15px;}
    .part-banner {font-size: 14px;}
    .part-banner h1 {font-size: 25px;}
    .part-banner .btn {width: 100%;min-width: auto;}
    .part-banner .breadCrumbs a, .part-banner .breadCrumbs span,.part-banner .breadCrumbs{opacity: 1;color: #000;}
		.part-feature #feature-tab .nav-item .desc {display: none;}
    .part-feature #feature-tab .nav-item {flex-direction: column;text-align: center;border-radius: 12px;}
    .part-feature #feature-tab .nav-item .icon {margin-right: 0;margin-bottom: 4px;width: 24px;}
    .part-feature #feature-tab .nav-item .title {font-size: 14px;line-height: 1.2;margin-bottom: 0;}
    .part-feature h2 {font-size: 18px;}
    .part-feature .feature-intro .row-cols-2 {margin-left: -6px;margin-right: -6px;}
    .part-feature .feature-intro .row-cols-2 .col {padding-left: 6px;padding-right: 6px;}
    .part-feature .feature-intro .box-style .icon {width: 40px;}
    .part-feature .feature-intro .box-style .title {font-size: 14px;}
    .part-feature .feature-intro .box-style {padding: 12px;border-radius: 12px;}

    .part-feature .editing-box {padding: 20px;border-radius: 15px;}
    .part-blogs .box-style {border-radius: 15px;}
    .part-blogs .box-style .box-content {padding:12px 0;}

    .part-steps {border-radius: 0;margin: 0;}
    .part-steps .swiper {overflow: hidden;}
    .part-steps .step-list::before {left: -20px;}
    .part-steps .step-list li::before {left: -20px;width: 3px;}
		.part-table .table-wrapper {border-radius: 15px;padding: 0;}
    .part-table .table thead th {min-width: 140px;font-size: 16px;}
		.part-table .tag-begin, .part-table .tag-profess {font-size: 12px;}
		.part-table table td, .part-table table thead th {padding: 10px;background-color: #FFFFFF21;}
		.part-table .table-wrapper .layer-item {display: none;}
		.part-table table tr td:nth-child(2), .part-table table thead th:nth-child(2) {background-color: #53FBD5;}
		.part-table table tr td:nth-child(3), .part-table table thead th:nth-child(3) {background-color: #00C2FF;}
    .faq-option {padding: 8px;}
    .faq-option .faq-item {font-size: 16px;padding-bottom: 8px;}
    .faq-option .faq-item.collapsed {padding-top: 8px;}

  }
</style>

<body data-pro="filmora" data-cat="article" data-nav="basic" data-sys="auto" data-dev="auto" data-spy="scroll" data-offset="72" data-target="#sticky-navbar">
<%= require('html-loader!./commonHTML/header.html') %>
<main class="wsc-main p-0 overflow-hidden">
	<section class="part-banner p-xl-3 m-xl-3">
    <div class="bg-hero rounded-xl-3 py-md-3">
      <div class="container py-xxl-4 text-center text-xl-left">
        <div class="row justify-content-center justify-content-xl-between align-items-center">
          <div class="col-md-10 col-xl-6 py-4">
            <div class="breadCrumbs d-flex align-items-center">
              <a href="https://filmora.wondershare.com/">Home</a><span class="mx-2">></span> <a href="https://filmora.wondershare.com/filmora-features.html">Features</a> <span class="mx-2">></span>Video Mask
            </div>
            <h1 class="pb-md-3 pb-4 mb-1 mt-3 display-3 font-weight-bold pr-xl-5">Video Mask:<br> Professional Video Mask Made Simple</h1>
            <div class="d-md-block d-none text-xl-left">Filmora offers a robust and flexible Video Masking system designed to meet the needs of both beginner creators and advanced editors.<br>
              • Mocha Pro Integration for Advanced Masking.<br>
              • Hybrid AI + Manual Masking Workflow.<br>
              • Precision Controls for Creative Flexibility
            </div>  
            <div class="d-flex align-items-end pt-md-3 pb-3 mt-md-1 mb-1 justify-content-xl-start justify-content-center">
              <img src="https://images.wondershare.com/filmora/images2023/feature/features_stars.svg" loading="lazy" alt="filmora features" class="img-fluid pr-2" style="height: 1.5rem;">
              <strong class="font-size-super pr-2 text-black" style="line-height: 1;">4.7</strong>
              <strong style="font-size: 14px;line-height: 14px;">(<a href="https://filmora.wondershare.com/reviews.html" class="text-black" target="_blank">15746 reviews</a>)</strong>
            </div>
            <div class="sys-win dev-desktop">
              <div class="row no-gutters justify-content-center justify-content-xl-start">
                <a href="https://download.wondershare.com/filmora_full846_guide-imagetovideo.exe" class="btn btn-lg btn-action m-0 mr-xl-3 mr-2"><i class="wsc-icon mr-2" data-icon="brand-windows"></i>Try It Free</a>
                <a href="https://filmora.wondershare.com/shop/buy/buy-video-editor.html" class="btn btn-lg btn-white border-black m-0">Buy Now</a>
              </div>
              <div class="font-size-small text-gray pt-3 mt-1">Win 11 /Win 10 / Win 8 / Win7 (64 bit OS) | <a href="https://filmora.wondershare.com/tech-spec.html" target="_blank" class="d-inline-block text-gray text-underline">System Requirements</a></div>
            </div>
            <div class="sys-mac dev-desktop">
              <div class="row no-gutters justify-content-center justify-content-xl-start">
                <a href="https://download.wondershare.com/filmora-mac_full14792_guide-imagetovideo.dmg" class="btn btn-lg btn-action m-0 mr-xl-3 mr-2 d-inline-flex align-items-center"><i class="wsc-icon mr-2" data-icon="brand-macos"></i>Try It Free</a>
                <a href="https://filmora.wondershare.com/shop/buy/buy-mac-video-editor.html" class="btn btn-lg btn-white border-black m-0">Buy Now</a>
              </div>
              <div class="font-size-small text-gray pt-3 mt-1">macOS 10.15 - macOS 15 (10.14 or earlier? <a href="https://filmora.wondershare.com/mac-tech-spec.html" target="_blank" class="d-inline-block text-gray text-underline">Click here</a>) | Apple M1, M2, M3 & M4 compatible</div>
            </div>
            <div class="dev-mobile">
              <div class="row no-gutters justify-content-center justify-content-xl-start">
                <a href="https://filmora.go.link/6zpbQ" class="btn btn-lg btn-action m-0 mr-3">Try It Free</a>
                <a href="https://filmora.wondershare.com/shop/buy/buy-video-editor.html" class="btn btn-lg btn-white border-black m-0">Buy Now</a>
              </div>
            </div>
          </div>
          <div class="col-md-10 col-xl-6 pb-4 pt-md-4">
            <video src="https://filmora.wondershare.com/videos/features/mocha-filmora/banner-video.mp4" poster="https://images.wondershare.com/filmora/features/mocha-filmora/mocha-filmora.png" autoplay muted loop class="img-fluid rounded-2" webkit-playsinline="" playsinline="true"></video>
            <div class="d-md-none pt-3 pb-4 text-left">Filmora offers a robust and flexible Video Masking system designed to meet the needs of both beginner creators and advanced editors.<br>
              • Mocha Pro Integration for Advanced Masking.<br>
              • Hybrid AI + Manual Masking Workflow.<br>
              • Precision Controls for Creative Flexibility</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="part-feature py-5 text-black" id="partFeature">
    <div class="container pt-md-3 mb-xxl-5 text-center">
      <h2 class="font-size-44">Complete Video Mask Solutions All in Filmora</h2>
      <div class="mb-xl-5 mb-4 font-size-large opacity-8">Choose between beginner-friendly built-in tools or professional-grade Mocha integration for masking in Filmora</div>
      <nav>
        <div class="nav" id="feature-tab" role="tablist">
          <a class="nav-item nav-link active" id="nav-feature1-tab" data-toggle="tab" href="#nav-feature1" role="tab" aria-controls="nav-feature1" aria-selected="true">
            <div class="icon"><img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask/feature-tab1-icon.svg" class="img-fluid" alt="feature tab icon"></div>
            <div>
              <div class="title">Filmora Built-in Mask</div>
              <div class="desc"> for beginner</div>
            </div>
          </a>
          <a class="nav-item nav-link" id="nav-feature2-tab" data-toggle="tab" href="#nav-feature2" role="tab" aria-controls="nav-feature2" aria-selected="false">
            <div class="icon"><img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask/feature-tab2-icon.svg" class="img-fluid" alt="feature tab icon"></div>
            <div>
              <div class="title">Mocha Filmora Video Mask</div>
              <div class="desc">for Pro</div>
            </div>
          </a>
        </div>
      </nav>
      <div class="tab-content pt-md-5 pt-3 text-left" id="nav-tabContent">
        <div class="tab-pane fade show active" id="nav-feature1" role="tabpanel" aria-labelledby="nav-feature1-tab">
          <div class="feature-intro mb-xl-3">
            <div class="intro-box mb-xl-2">
							<h2 class="mb-0">AI-Driven Edge Detection & Temporal Similarity-Based Mask</h2>       
							<div class="desc">Filmora's built-in masking system is designed for creators who want professional results without the complexity. Our intuitive tools make video mask creation accessible to everyone.</div>      
            </div>
            <div class="row row-cols-lg-4 row-cols-2">
              <div class="col pt-xl-4 pt-3">
                <div class="box-style" style="background-color: #f0f6ff;">
                  <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/feature-visual-interface-icon.svg" class="img-fluid" alt="feature icon"></div>
                  <div class="title">visual interface</div>
                </div>
              </div>
              <div class="col pt-xl-4 pt-3">
                <div class="box-style" style="background-color: #fffbd5;">
                  <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/feature-real-time-icon.svg" class="img-fluid" alt="feature icon"></div>
                  <div class="title">Real- time Preview</div>
                </div>
              </div>
              <div class="col pt-xl-4 pt-3">
                <div class="box-style" style="background-color: #d6fff4;">
                  <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/feature-ai-assitance.svg" class="img-fluid" alt="feature icon"></div>
                  <div class="title">AI Assitance</div>
                </div>
              </div>
              <div class="col pt-xl-4 pt-3">
                <div class="box-style" style="background-color: #fff1fa;">
                  <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/feature-easy-controls.svg" class="img-fluid" alt="feature icon"></div>
                  <div class="title">Easy Controls</div>
                </div>
              </div>
            </div>
          </div>

          <div class="row align-items-center py-3 py-md-4">
            <div class="col-xl-5 col-md-6 offset-xl-1 py-3 py-md-4 order-1 order-md-2">
              <h3 class="h2 mb-3 line-height-150">Basic Shape Masks for Effortless Visual Impact</h3>
              <div class="text-gray-heavy">Simply adjust corner pins in the video and Filmora handles the rest automatically with intelligent surface detection. Identify planar areas in videos and images with great accuracy, achieve smoother movement tracking.<br>
              • Circle, Rectangle, Polygon shapes<br>
              • Adjustable corner radius and proportions<br>
              • Real-time shape manipulation<br>
              • Perfect for spotlight effects and framing</div>
            </div>
            <div class="col-md-6 py-md-4 order-2 order-md-1">
							<video class="lazy-video img-fluid rounded-16" muted autoplay loop poster="https://images.wondershare.com/filmora/features/video-mask/basic-mask-poster.png" data-src="https://filmora.wondershare.com/videos/features/video-mask/basic-mask.mp4" webkit-playsinline="" playsinline="true"></video>
            </div>
          </div>
          <div class="row align-items-center justify-content-between py-3 py-md-4">
            <div class="col-xl-5 col-md-6 py-3 py-md-4">
              <h3 class="h2 mb-3 line-height-150">Import Your Own Shapes And Graphics as Mask Templates</h3>
              <div class="text-gray-heavy">Easily import your own shapes and graphic assets as mask templates to match your creative vision.<br>
              • Full support for common formats like PNG, SVG, and other vector-based files<br>
              • Alpha channel detection for instant transparency handling<br>
              • Resolution-independent scaling ensures crisp quality at any size<br>
              • Ideal for integrating branded elements, logos, or custom cutout effects into your videos</div>
            </div>
            <div class=" offset-xl-1 col-md-6 py-md-4">
              <video class="lazy-video img-fluid rounded-16" muted autoplay loop poster="https://images.wondershare.com/filmora/features/video-mask/import-mask-poster.png" data-src="https://filmora.wondershare.com/videos/features/video-mask/import-mask.mp4" webkit-playsinline="" playsinline="true"></video>
            </div>
          </div>
          <div class="row align-items-center py-3 py-md-4">
            <div class="col-xl-5 col-md-6 offset-xl-1 py-3 py-md-4 order-1 order-md-2">
              <h3 class="h2 mb-3 line-height-150">Sculpt with Precision Using the Pen Mask Tool</h3>
              <div class="text-gray-heavy">Create highly customized masks by hand with Filmora’s intuitive pen tool, giving you full control over intricate shapes and outlines.<br>
              • Freehand drawing with fluid bezier curves for natural motion paths<br>
              • Fully adjustable brush size, softness, and pressure for detailed refinement<br>
              • Undo/redo support to fine-tune every stroke with ease<br>
              • Ideal for masking complex subjects, organic contours, and creative transitions</div>
            </div>
            <div class="col-md-6 py-md-4 order-2 order-md-1">
              <video class="lazy-video img-fluid rounded-16" muted autoplay loop poster="https://images.wondershare.com/filmora/features/video-mask/draw-mask-poster.png" data-src="https://filmora.wondershare.com/videos/features/video-mask/draw-mask.mp4" webkit-playsinline="" playsinline="true"></video>
            </div>
          </div>
          <div class="row align-items-center justify-content-between py-3 py-md-4">
            <div class="col-xl-5 col-md-6  py-3 py-md-4">
              <h3 class="h2 mb-3 line-height-150">Smart Selections Made Simple with AI Masking</h3>
              <div class="text-gray-heavy">Accelerate your workflow with intelligent masking that automatically identifies people, objects, and backgrounds in your videos, no manual drawing required.<br>
              • One-click detection to instantly isolate human figures with accurate contours<br>
              • Smart object recognition for cars, animals, and common elements in your footage<br>
              • AI Brush tool for manual adjustments and fine-tuning with precision<br>
              • Automatic background removal for fast subject isolation and clean compositions</div>
            </div>
            <div class="col-md-6 offset-xl-1 py-md-4">
							<video class="lazy-video img-fluid rounded-16" muted autoplay loop poster="https://images.wondershare.com/filmora/features/video-mask/ai-mask-poster.png" data-src="https://filmora.wondershare.com/videos/features/video-mask/ai-mask.mp4" webkit-playsinline="" playsinline="true"></video>
            </div>
          </div>

          <div class="editing-box mt-5">
            <h2 class="font-size-44 mb-md-3 text-center">Ultimate Precision Mask Editing Controls</h2>
            <div class="row row-cols-md-3 row-cols-1">
              <div class="col pt-4">
                <div class="box-style">
                  <div class="box-content">
                    <div class="box-top">
                    <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/edit-edge-refinement.svg" alt="editing icon"></div>
                    <div class="title">Edge Refinement</div>
                  </div>
                  <ul>
                    <li>Edge blur intensity (0-100%)</li>
                    <li>Feather softness control</li>
                    <li>Antialiasing smoothing</li>
                    <li>Edge detection sensitivity</li>
                  </ul>
                  </div>
                </div>
              </div>
              <div class="col pt-4">
                <div class="box-style">
                  <div class="box-content">
                    <div class="box-top">
                    <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/edit-mask-property.svg" alt="editing icon"></div>
                    <div class="title">Mask Properties</div>
                  </div>
                  <ul>
                    <li>Brush size and hardness</li>
                    <li>Corner radius adjustment</li>
                    <li>Transparency levels (0-100%)</li>
                    <li>Overall mask size scaling</li>
                    <li>Width and height proportions</li>
                  </ul>
                  </div>
                </div>
              </div>
              <div class="col pt-4">
                <div class="box-style">
                  <div class="box-content">
                    <div class="box-top">
                    <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/edit-transform-position.svg" alt="editing icon"></div>
                    <div class="title">Transform & Position</div>
                  </div>
                  <ul>
                    <li>Shape rotation (0-360°)</li>
                    <li>X/Y position coordinates</li>
                    <li>Mask inversion toggle</li>
                    <li>Anchor point adjustment</li>
                    <li> Keyframe animation support</li>
                  </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tab-pane fade" id="nav-feature2" role="tabpanel" aria-labelledby="nav-feature2-tab">
          <div class="feature-intro mb-xl-3">
            <div class="intro-box mb-xl-2">
							<h2 class="mb-0">
								<div>Planar Tracking-Based Mask for Non-Rigid and Occluded Object Accuracy</div>
							</h2>       
							<div class="desc">Wondershare Filmora masking now includes industry-standard Mocha technology, bringing Hollywood-level masking capabilities to your desktop. Perfect for complex tracking and professional visual effects.</div>      
            </div>
            <div class="row row-cols-lg-4 row-cols-2 pt-1">
                <div class="col pt-3">
                  <div class="box-style" style="background-color: #f0f6ff;">
                    <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/feature-planar-tracking.svg" class="img-fluid" alt="feature icon"></div>
                    <div class="title">planar tracking</div>
                  </div>
                </div>
                <div class="col pt-3">
                  <div class="box-style" style="background-color: #fffbd5;">
                    <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/feature-ml-segmentation.svg" class="img-fluid" alt="feature icon"></div>
                    <div class="title">ML Segmentation</div>
                  </div>
                </div>
                <div class="col pt-3">
                  <div class="box-style" style="background-color: #fff1fa;">
                    <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/feature-spline-curves.svg" class="img-fluid" alt="feature icon"></div>
                    <div class="title">Spline Curves</div>
                  </div>
                </div>
                <div class="col pt-3">
                  <div class="box-style" style="background-color: #d6fff4;">
                    <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/feature-muti-export.svg" class="img-fluid" alt="feature icon"></div>
                    <div class="title">Multi-Export</div>
                  </div>
                </div>
              </div>
          </div>

          <div class="row align-items-center py-3 py-md-4">
            <div class="col-xl-5 col-md-6 offset-xl-1 py-3 py-md-4 order-1 order-md-2">
              <div class="text-left">
                <h3 class="h2 mb-md-3 line-height-150">PowerMesh Tracking & Mesh Warp: Precision Masks That Follow Every Move</h3>
                <div class="text-gray-heavy">Leverage the power of Mocha Pro’s industry-standard planar tracking to create masks that stay locked to complex movements and shifting perspectives with unmatched stability.<br>
                • Accurately follows organic movement like faces, fabrics, or bending surfaces.<br>
                • Maintain perfect perspective with intelligent handling of distortion and angle shifts<br>
                • Use PowerMesh to drive roto shapes with fewer keyframes.<br>
                • Perfect for applying motion graphics to moving surfaces with distortion.<br>
                • Robust occlusion handling keeps masks accurate even when the object is partially blocked</div>
              </div>
            </div>
            <div class="col-md-6 py-md-4 order-2 order-md-1">
              <video class="lazy-video img-fluid rounded-16" muted autoplay loop poster="https://images.wondershare.com/filmora/features/mocha-filmora/powermesh-tracking.png" data-src="https://filmora.wondershare.com/videos/features/mocha-filmora/powermesh-tracking.mp4" webkit-playsinline="" playsinline="true"></video>
            </div>
          </div>
          <div class="row align-items-center justify-content-between py-3 py-md-4">
            <div class="col-xl-5 col-md-6 py-3 py-md-4">
              <div class="text-left">
                <h3 class="h2 mb-md-3 line-height-150">Make Complex Mask & Roto Simple with Perfect Results</h3>
                <div class="text-gray-heavy">With Mocha Pro technology built into Filmora, creators can now access professional-grade AI masking tools—like Object Brush and Matte Assist—right inside their timeline. Effortlessly generate accurate, editable masks with one click, all processed locally for enhanced privacy and performance.<br>
                • Automatic object recognition identifies people, pets, and items with minimal input<br>
                • Refined edge detection preserves intricate details like hair strands and fur<br>
                • Smart background separation ensures clean cuts even in visually busy footage<br>
                • Ideal for green screen alternatives, product cutouts, and compositing work</div>
								</div>
            </div>
            <div class="col-md-6 offset-xl-1 py-md-4">
              <img src="https://images.wondershare.com/filmora/features/mocha-filmora/ai-masking-roto.png" class="img-fluid rounded-16" loading="lazy" alt="Mask & Roto">
            </div>
          </div>
          <div class="row align-items-center py-3 py-md-4">
            <div class="col-xl-5 col-md-6 offset-xl-1 py-3 py-md-4 order-1 order-md-2">
              <div class="text-left">
                <h3 class="h2 mb-md-3 line-height-150">Spline & Pen Tool Masking: Design-Grade Control for Perfect Masks</h3>
                <div class="text-gray-heavy">Easily create smooth, high-precision, and fully editable masks using X-Splines and Bezier curve controls, enhanced by magnetic edge-snapping and the intuitive Area Brush tool, no drawing skills required. Ideal for advanced compositing and shape-based animations.<br>
                • Vector-based accuracy for pixel-perfect masking and smooth edge control<br>
                • Bezier curve handles for flexible, non-destructive shape refinement<br>
                • Scales infinitely without any quality loss—great for high-res workflows<br>
                • Supports keyframe animation for dynamic shape morphing and motion graphics
								</div>
              </div>
            </div>
            <div class="col-md-6 py-md-4 order-2 order-md-1">
              <video class="lazy-video img-fluid rounded-16" muted autoplay loop poster="https://images.wondershare.com/filmora/features/video-mask/mocha-mask-poster.png" data-src="https://filmora.wondershare.com/videos/features/video-mask/mocha-mask.mp4" webkit-playsinline="" playsinline="true"></video>
            </div>
          </div>

          <div class="editing-box mt-5">
            <h2 class="font-size-44 mb-3 text-center">Ultimate Precision Mask Editing Controls</h2>
            <div class="row row-cols-md-3 row-cols-1">
              <div class="col pt-4">
                <div class="box-style">
                  <div class="box-content">
                    <div class="box-top">
                    <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/edit-planar-tracker.svg" alt="editing icon"></div>
                    <div class="title">Planar Tracker</div>
                  </div>
                  <ul>
                    <li>Corner pin tracking</li>
                    <li>Perspective correction</li>
                    <li>Motion blur compensation</li>
                    <li>Sub-pixel accuracy</li>
                    <li>Automatic keyframe generation</li>
                  </ul>
                  </div>
                </div>
              </div>
              <div class="col pt-4">
                <div class="box-style">
                  <div class="box-content">
                    <div class="box-top">
                    <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/edit-object-brush.svg" alt="editing icon"></div>
                    <div class="title">Object Brush (ML)</div>
                  </div>
                  <ul>
                    <li>Neural network segmentation</li>
                    <li>Edge-aware refinement</li>
                    <li>Temporal consistency</li>
                    <li>Multi-object handling</li>
                    <li>Automatic propagation</li>
                  </ul>
                  </div>
                </div>
              </div>
              <div class="col pt-4">
                <div class="box-style">
                  <div class="box-content">
                    <div class="box-top">
                      <div class="icon"><img src="https://images.wondershare.com/filmora/features/video-mask/edit-matte-assist.svg" alt="editing icon"></div>
                      <div class="title">Matte Assist (ML)</div>
                    </div>
                    <ul>
                      <li>Intelligent matte cleanup</li>
                      <li>Garbage matte automation</li>
                      <li>Edge enhancement</li>
                      <li>Noise reduction</li>
                      <li>Quality optimization</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="part-steps py-5 mx-30" id="partSteps">
    <div class="container py-xl-3 my-3 text-center text-black">
      <h2 class="font-size-44 mb-3">How To Use the Planar Tracking in Filmora</h2>
      <div class="mb-4 font-size-large opacity-8">Learn how to use both tracking methods with our detailed guides.</div>
      <nav>
        <div class="nav" id="nav-steps-tab" role="tablist">
          <a class="nav-item nav-link active" id="nav-step-tab1" data-toggle="tab" href="#nav-stepTab1" role="tab" aria-controls="nav-step1" aria-selected="true">Filmora Built-in Lens Correction</a>
          <a class="nav-item nav-link" id="nav-step-tab2" data-toggle="tab" href="#nav-stepTab2" role="tab" aria-controls="nav-step2" aria-selected="false">Mocha Filmora Lens Correction</a>
        </div>
      </nav>
      <div class="tab-content pt-md-5 pt-4" id="nav-steps-tabContent">
        <div class="tab-pane fade show active" id="nav-stepTab1" role="tabpanel" aria-labelledby="nav-step-tab1">
          <div class="mt-md-n4 pb-xl-4 mb-md-3 text-center">
            <div class="font-size-large d-inline-flex align-items-center justify-content-center font-weight-bold" data-toggle="youtube" data-type="modal" data-youtube="a56O9ZTAC_I"><svg style="width: 2.25rem;" height="100%" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18 30C24.6274 30 30 24.6274 30 18C30 11.3726 24.6274 6 18 6C11.3726 6 6 11.3726 6 18C6 24.6274 11.3726 30 18 30Z" stroke="#07273D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M15.6 13.2L22.8 18L15.6 22.8V13.2Z" stroke="#07273D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
              <u>View video tutorial >></u></div>
          </div>
          <div class="row align-items-center justify-content-center">
            <div class="col-xl-7 col-md-10 position-relative">
              <div class="pr-xl-5 pr-md-3">
                <div class="swiper stepSwiper1">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide">
                      <div class="step-content d-xl-none">
                        <h5><span class="step-num">Step 1</span> Import Your Video</h5>
                        <div>Drag and drop your video file into Filmora's timeline. Select the clip you want to apply video mask effects to.</div>
                      </div>
                      <img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask/tab1-step1.png" alt="pc step pic" class="img-fluid">
                    </div>
                    <div class="swiper-slide">
                      <div class="step-content d-xl-none">
                        <h5><span class="step-num">Step 2</span> Access Mask Tools</h5>
                        <div>Navigate to Effects panel and find the Mask section. Choose from AI Mask, Shape Mask, or Pen Mask options.</div>
                      </div>
                      <img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask//tab1-step2.png" alt="pc step pic" class="img-fluid">
                    </div>
                    <div class="swiper-slide">
                      <div class="step-content d-xl-none">
                        <h5><span class="step-num">Step 3</span> Create Your Mask</h5>
                        <div>Draw, select, or let AI automatically detect the area you want to mask. Use real-time preview to perfect your selection.</div>
                      </div>
                      <img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask/tab1-step3.png" alt="pc step pic" class="img-fluid">
                    </div>
                    <div class="swiper-slide">
                      <div class="step-content d-xl-none">
                        <h5><span class="step-num">Step 4</span> Fine-tune & Export</h5>
                        <div>Adjust edge blur, transparency, and other parameters. Preview your result and export your masked video.</div>
                      </div>
                      <img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask/tab1-step4.png" alt="pc step pic" class="img-fluid">
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xl-5 col-md-6 d-xl-block d-none">
              <ul class="step-list step1-list">
                <li class="active">
                  <h5>1：Import Your Video</h5>
                  <div>Drag and drop your video file into Filmora's timeline. Select the clip you want to apply video mask effects to.</div>
                </li>
                <li>
                  <h5>2：Access Mask Tools</h5>
                  <div>Navigate to Effects panel and find the Mask section. Choose from AI Mask, Shape Mask, or Pen Mask options.</div>
                </li>
                <li>
                  <h5>3：Create Your Mask</h5>
                  <div>Draw, select, or let AI automatically detect the area you want to mask. Use real-time preview to perfect your selection.</div>
                </li>
                <li>
                  <h5>4： Fine-tune & Export</h5>
                  <div>Adjust edge blur, transparency, and other parameters. Preview your result and export your masked video.</div>
                </li>
            </ul>
            </div>
          </div>
        </div>
        <div class="tab-pane fade" id="nav-stepTab2" role="tabpanel" aria-labelledby="nav-step-tab2">
          <div class="mt-md-n4 pb-xl-4 mb-md-3 text-center">
            <div class="font-size-large d-inline-flex align-items-center justify-content-center font-weight-bold" data-toggle="youtube" data-type="modal" data-youtube="m7X03iHNgW4"><svg style="width: 2.25rem;" height="100%" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18 30C24.6274 30 30 24.6274 30 18C30 11.3726 24.6274 6 18 6C11.3726 6 6 11.3726 6 18C6 24.6274 11.3726 30 18 30Z" stroke="#07273D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M15.6 13.2L22.8 18L15.6 22.8V13.2Z" stroke="#07273D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
              <u>View video tutorial >></u></div>
          </div>
          <div class="row align-items-center justify-content-center">
            <div class="col-xl-7 col-md-10 position-relative">
              <div class="pr-xl-5 pr-md-3">
                <div class="swiper stepSwiper2">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide">
                      <div class="step-content d-xl-none">
                        <h5><span class="step-num">Step 1</span>Access Mocha Plugin</h5>
                        <div>Navigate to Effects > Boris FX > Mocha Filmora and click "Download Now" to install the plugin. No registration required for Filmora users.</div>
                      </div>
                      <img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask/tab2-step1.png" alt="pc step pic" class="img-fluid">
                    </div>
                    <div class="swiper-slide">
                      <div class="step-content d-xl-none">
                        <h5><span class="step-num">Step 2</span> Apply Mocha to Your Clip</h5>
                        <div>Drag the Mocha Filmora effect onto your video clip. The Properties panel will dynamically load the Tracking module interface.</div>
                      </div>
                      <img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask/tab2-step2.png" alt="pc step pic" class="img-fluid">
                    </div>
                    <div class="swiper-slide">
                      <div class="step-content d-xl-none">
                        <h5><span class="step-num">Step 3</span>  Launch Mocha Interface</h5>
                        <div>Click to enter the Mocha plugin interface where you can create tracking shapes and define the mask surface for analysis.</div>
                      </div>
                      <img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask/tab2-step3.png" alt="pc step pic" class="img-fluid">
                    </div>
                    <div class="swiper-slide">
                      <div class="step-content d-xl-none">
                        <h5><span class="step-num">Step 4</span> Create Mask Shapes</h5>
                        <div>Use Mocha's spline tools to outline the planar surface. Draw around areas with good texture and contrast for optimal mask results.</div>
                      </div>
                      <img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask/tab2-step4.png" alt="pc step pic" class="img-fluid">
                    </div>
                    <div class="swiper-slide">
                      <div class="step-content d-xl-none">
                        <h5><span class="step-num">Step 5</span> Execute Plane Tracking</h5>
                        <div>Start the video masking process. Mocha will analyze the entire surface texture. You can then integrate it with Mocha’s Insert module or Masking & Roto tools for creative work. If you are new to Mocha, you can <a href="https://borisfx.com/free-training/mocha-essentials/" target="_blank" class="text-action"><u>View full Mocha tutorial >></u></a></div>
                      </div>
                      <img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask/tab2-step5.png" alt="pc step pic" class="img-fluid">
                    </div>
                    <div class="swiper-slide">
                      <div class="step-content d-xl-none">
                        <h5><span class="step-num">Step 6</span> Export to Filmora</h5>
                        <div>Transfer matte data back to Filmora and continue your editing or export video directly.</div>
                      </div>
                      <img loading="lazy" src="https://images.wondershare.com/filmora/features/video-mask/tab2-step6.png" alt="pc step pic" class="img-fluid">
                    </div>
                  </div>
                  <div class="swiper-pagination d-lg-none"></div>
                </div>
              </div>
            </div>
            <div class="col-xl-5 col-md-6 d-xl-block d-none">
              <ul class="step-list step2-list">
                <li class="active">
                  <h5>1：Access Mocha Plugin</h5>
                  <div>Navigate to Effects > Boris FX > Mocha Filmora and click "Download Now" to install the plugin. No registration required for Filmora users.</div>
                </li>
                <li>
                  <h5>2：Apply Mocha to Your Clip</h5>
                  <div>Drag the Mocha Filmora effect onto your video clip. The Properties panel will dynamically load the Tracking module interface.</div>
                </li>
                <li>
                  <h5>3： Launch Mocha Interface</h5>
                  <div>Click to enter the Mocha plugin interface where you can create tracking shapes and define the mask surface for analysis.</div>
                </li>
                <li>
                  <h5>4：Create Mask Shapes</h5>
                  <div>Use Mocha's spline tools to outline the planar surface. Draw around areas with good texture and contrast for optimal mask results.</div>
                </li>
                <li>
                  <h5>5：Execute Plane Tracking</h5>
                  <div>Start the video masking process. Mocha will analyze the entire surface texture. You can then integrate it with Mocha’s Insert module or Masking & Roto tools for creative work. If you are new to Mocha, you can <a href="https://borisfx.com/free-training/mocha-essentials/" target="_blank" class="text-action"><u>View full Mocha tutorial >></u></a></div>
                </li>
                <li>
                  <h5>6：Export to Filmora</h5>
                  <div>Transfer matte data back to Filmora and continue your editing or export video directly.</div>
                </li>
            </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="pt-xl-5 pt-4 d-md-flex align-items-center justify-content-center d-none text-center">
        <a href="https://download.wondershare.com/filmora_full846.exe" class="btn btn-lg btn-action d-inline-flex align-items-center justify-content-center dev-desktop sys-win"><i class="wsc-icon mr-2" data-icon="brand-windows"></i> Start Creating Your AI Video</a>
        <a href="https://download.wondershare.com/filmora-mac_full14792.dmg" class="btn btn-lg btn-action d-inline-flex align-items-center justify-content-center dev-desktop sys-mac"><i class="wsc-icon mr-2" data-icon="brand-macos"></i> Start Creating Your AI Video</a>
        <a href="https://app.adjust.com/1l6lvu2p_1linnim7" class="btn btn-lg btn-action dev-mobile">Start Creating Your AI Video</a>
      </div>
			<div class="pt-3 font-size-large font-weight-bold text-center"><a href="https://filmora.wondershare.com/guide/ai-idea-to-video.html" target="_blank"><u>Check guide in detail &gt;&gt;&gt;</u></a></div>
    </div>
  </section>
  <section class="part-table pt-5" id="partTable">
    <div class="container py-xl-5 py-3 my-md-4 text-center text-black">
      <h2 class="font-size-44 mb-xl-4">Feature Comparison - Built-in Video Mask vs. <br class="d-md-block d-none"> Mocha Filmora</h2>
      <div class="font-size-large opacity-8">Compare the capabilities of both video mask technologies to choose the perfect solution for your project needs.</div>
      <div class="table-wrapper mt-xl-5 my-4">
        <div class="table-responsive">
          <table class="table mb-0">
            <thead>
              <tr>
                <th>Feature </th>
                <th>Filmora Built-in <span class="tag-begin"><span class="text-linear">For Beginners</span></span></th>
                <th>Mocha Filmora <span class="tag-profess"><span class="text-linear">For Professionals</span></span></th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Ease of Use</td>
                <td><div><span class="tag-text">Excellent</span></div></td>
                <td><div><span class="tag-text">Advanced</span></div></td>
              </tr>
              <tr>
                <td>AI Detection</td>
                <td><div><span class="tag-text">yes</span></div></td>
                <td><div><span class="tag-text">ML Enhanced</span></div></td>
              </tr>
							<tr>
                <td>Motion Tracking</td>
                <td><div><span class="tag-text">Basic</span></div></td>
                <td><div><span class="tag-text">Professional</span></div></td>
              </tr>
              <tr>
                <td>Precision Control</td>
                <td><div><span class="tag-text">Good</span></div></td>
                <td><div><span class="tag-text">Excellent</span></div></td>
              </tr>
							<tr>
                <td>Export Options</td>
                <td><div><span class="tag-text">Standard</span></div></td>
                <td><div><span class="tag-text">Advanced</span></div></td>
              </tr>
              <tr>
                <td>Learning Curve</td>
                <td><div><span class="tag-text">Easy</span></div></td>
                <td><div><span class="tag-text">Moderate</span></div></td>
              </tr>
							<tr>
                <td>Spline Curves</td>
                <td><div><span class="tag-text no">NO</span></div></td>
                <td><div><span class="tag-text">yes</span></div></td>
              </tr>
              <tr>
                <td>Planar Tracking</td>
                <td><div><span class="tag-text no">NO</span></div></td>
                <td><div><span class="tag-text">yes</span></div></td>
              </tr>
							<tr>
                <td>ML Segmentation</td>
                <td><div><span class="tag-text">Basic</span></div></td>
                <td><div><span class="tag-text">Advanced</span></div></td>
              </tr>
              <tr>
                <td>Mask & Roto</td>
                <td><div><b>Mask:</b> <span class="tag-text">yes</span><span class="px-1"></span><b>roto:</b><span class="tag-text no">NO</span></div></td>
                <td><div><span class="tag-text">yes</span></div></td>
              </tr>
              <tr>
                <td>Powermesh Tracking</td>
                <td><div><span class="tag-text no">NO</span></div></td>
                <td><div><span class="tag-text">yes</span></div></td>
              </tr>
              <tr>
                <td>Mesh Wrap</td>
                <td><div><span class="tag-text no">NO</span></div></td>
                <td><div><span class="tag-text">yes</span></div></td>
              </tr>
            </tbody>
          </table>
					<div class="layer-item layer1"></div>
					<div class="layer-item layer2"></div>
					<div class="layer-item layer3"></div>
        </div>
      </div>
			<div class="pt-3">
				<a href="https://download.wondershare.com/filmora_full846_guide-imagetovideo.exe" class="btn btn-lg btn-linear white dev-desktop sys-win"><i class="wsc-icon mr-2" data-icon="brand-windows"></i> Try It Free</a>
				<a href="https://download.wondershare.com/filmora-mac_full14792_guide-imagetovideo.dmg" class="btn btn-lg btn-linear white dev-desktop sys-mac"><i class="wsc-icon mr-2" data-icon="brand-macos"></i> Try It Free</a>
				<a href="https://filmora.go.link/6zpbQ" class="btn btn-lg btn-linear white dev-mobile">Try It Free</a>
			</div>
    </div>
  </section>
	<section class="part-need pt-5">
		<div class="container py-xl-5 text-center text-black">
			<h2 class="font-size-44">Meet Every Video Enhancement Need</h2>
      <p class="font-size-large opacity-8">Fotor's video enhancer can meet various video enhancement needs. Whether it's for personal use or for commercial marketing, Fotor has got you covered!</p>
			<div class="row row-cols-lg-3 row-cols-md-2 row-cols-1 py-md-3">
				<div class="col py-md-3 pt-4">
					<div class="box-style">
						<div class="img-container"><img src="https://images.wondershare.com/filmora/features/video-mask/need-pic1.png" alt="need-pic"></div>
						<div class="box-content">
							<div>
                <h5>Background Replacement</h5>
							  <div class="desc">Remove and replace backgrounds seamlessly with AI-powered mask filmora tools</div>
              </div>
              <div class="tag-list">
                <div class="tag-item">cross dissolve transition</div>
                <div class="tag-item">dissolve transitions</div>
              </div>
						</div>
					</div>
				</div>
				<div class="col py-md-3 pt-4">
					<div class="box-style">
						<div class="img-container"><img src="https://images.wondershare.com/filmora/features/video-mask/need-pic2.png" alt="need-pic"></div>
						<div class="box-content">
							<div>
                <h5>Object Removal</h5>
							  <div class="desc">Eliminate unwanted objects from your footage using advanced masking in filmora</div>
              </div>
              <div class="tag-list">
                <div class="tag-item">Temporal consistency</div>
                <div class="tag-item">Planar tracking</div>
                <div class="tag-item">Content-aware fill</div>
              </div>
						</div>
					</div>
				</div>
				<div class="col py-md-3 pt-4">
					<div class="box-style">
						<div class="img-container"><img src="https://images.wondershare.com/filmora/features/video-mask/need-pic3.png" alt="need-pic"></div>
						<div class="box-content">
							<div>
                <h5>Creative Effects</h5>
							  <div class="desc">Apply selective color correction and effects to specific areas</div>
              </div>
              <div class="tag-list">
                <div class="tag-item">Gradient effects</div>
                <div class="tag-item">Dynamic tracking</div>
                <div class="tag-item">Selective masking</div>
              </div>
						</div>
					</div>
				</div>
				<div class="col py-md-3 pt-4">
					<div class="box-style">
						<div class="img-container"><img src="https://images.wondershare.com/filmora/features/video-mask/need-pic4.png" alt="need-pic"></div>
						<div class="box-content">
							<div>
                <h5>Privacy Protection</h5>
							  <div class="desc">Blur faces and sensitive information while maintaining video quality</div>
              </div>
              <div class="tag-list">
                <div class="tag-item">cross dissolve transition</div>
                <div class="tag-item">dissolve transitions</div>
              </div>
						</div>
					</div>
				</div>
				<div class="col py-md-3 pt-4">
					<div class="box-style">
						<div class="img-container"><img src="https://images.wondershare.com/filmora/features/video-mask/need-pic5.png" alt="need-pic"></div>
						<div class="box-content">
              <div>
                <h5>Motion Graphics</h5>
                <div class="desc">Create dynamic text and graphics that follow moving objects</div>
              </div>
              <div class="tag-list">
                <div class="tag-item">cross dissolve transition</div>
                <div class="tag-item">dissolve transitions</div>
              </div>
						</div>
					</div>
				</div>
				<div class="col py-md-3 pt-4">
					<div class="box-style">
						<div class="img-container"><img src="https://images.wondershare.com/filmora/features/video-mask/need-pic6.png" alt="need-pic"></div>
						<div class="box-content">
							<div>
                <h5>Color Grading</h5>
							  <div class="desc">Apply targeted color adjustments to enhance specific elements</div>
              </div>
              <div class="tag-list">
                <div class="tag-item">cross dissolve transition</div>
                <div class="tag-item">dissolve transitions</div>
              </div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
  <div class="part-blogs pt-5">
    <div class="container pt-3 mt-3 text-center">
      <h2 class="font-size-44 mb-3">Learn More About Planar Tracking in Our Blogs</h2>
      <div class="row row-cols-lg-3 row-cols-md-2 row-cols-1 py-md-3">
        <div class="col py-md-3 pt-4">
					<div class="box-style">
						<div class="img-container"><img src="https://images.wondershare.com/filmora/features/video-mask/blog-pic1.png" alt="need-pic"></div>
						<div class="box-content">
							<h5>Motion Tracking vs. Planar Tracking</h5>
							<div class="desc">Understand the differences between planar and motion tracking in Filmora and decide which one is right for your project.</div>
						</div>
					</div>
				</div>
        <div class="col py-md-3 pt-4">
					<div class="box-style">
						<div class="img-container"><img src="https://images.wondershare.com/filmora/features/video-mask/blog-pic2.png" alt="need-pic"></div>
						<div class="box-content">
							<h5>Planar Tracking in Mocha AE</h5>
							<div class="desc">Learn about the Track Matte in After Effects and how to use it to create precise and impactful tracking effects in your projects.</div>
						</div>
					</div>
				</div>
        <div class="col py-md-3 pt-4">
					<div class="box-style">
						<div class="img-container"><img src="https://images.wondershare.com/filmora/features/video-mask/blog-pic3.png" alt="need-pic"></div>
						<div class="box-content">
							<h5>Planar Tracking in Mocha AE</h5>
							<div class="desc">Mocha is a plug-in included with After Effects that is highly regarded for its advanced tracking and masking capabilities.</div>
						</div>
					</div>
				</div>
      </div>
    </div>
  </div>

  <section class="part-faq py-5 mt-3" id="partFaq">
    <div class="container py-md-5">
      <h2 class="h1 text-center text-black">Frequently Asked Questions</h2>
      <div class="row justify-content-center pt-md-3 my-md-4">
        <div class="col-md-10 col-xl-8">
          <div id="accordion-faq" role="tablist">
            <div class="faq-option">
              <div id="heading-faq1" class="faq-item d-flex justify-content-between with-hand font-weight-extra-bold" data-toggle="collapse" data-target="#collapse-faq1" aria-expanded="true" aria-controls="collapse-faq1" role="tab">
								What is video mask in Filmora?
                <i class="wsc-icon h-auto ml-3">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 6L8 12L13 6" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="12.2422" y="2.34326" width="2" height="14" rx="1" transform="rotate(45 12.2422 2.34326)" fill="#07273D"/>
                    <rect x="13.6572" y="12.2427" width="2" height="14" rx="1" transform="rotate(135 13.6572 12.2427)" fill="#07273D"/>
                  </svg>
                </i>
              </div>
              <div id="collapse-faq1" class="collapse show" aria-labelledby="heading-faq1" data-parent="#accordion-faq">
                Video mask in Filmora is a powerful feature that allows you to selectively hide or show parts of your video. It includes AI-powered automatic detection, manual drawing tools, and professional Mocha integration for advanced tracking. You can create basic shapes, import custom shapes, use the pen tool for freehand drawing, or leverage AI for smart object detection.</div>
            </div>
            <div class="faq-option">
              <div id="heading-faq2" class="faq-item d-flex justify-content-between with-hand font-weight-extra-bold collapsed" data-toggle="collapse" data-target="#collapse-faq2" aria-expanded="false" aria-controls="collapse-faq2" role="tab">
                How does AI masking work in Filmora?
                <i class="wsc-icon h-auto ml-3">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 6L8 12L13 6" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="12.2422" y="2.34326" width="2" height="14" rx="1" transform="rotate(45 12.2422 2.34326)" fill="#07273D"/>
                    <rect x="13.6572" y="12.2427" width="2" height="14" rx="1" transform="rotate(135 13.6572 12.2427)" fill="#07273D"/>
                  </svg>
                </i>
              </div>
              <div id="collapse-faq2" class="collapse" aria-labelledby="heading-faq2" data-parent="#accordion-faq">
                Filmora's AI masking uses machine learning to automatically detect and isolate objects, people, or backgrounds in your video. Simply select the AI mask tool, and it will intelligently create masks based on what it recognizes in your footage. The AI can distinguish between people, objects, and backgrounds, and includes an AI brush for manual refinement of edges.</div>
						</div>
            <div class="faq-option">
              <div id="heading-faq3" class="faq-item d-flex justify-content-between with-hand font-weight-extra-bold collapsed" data-toggle="collapse" data-target="#collapse-faq3" aria-expanded="false" aria-controls="collapse-faq3" role="tab">
                What's the difference between built-in and Mocha masking?
                <i class="wsc-icon h-auto ml-3">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 6L8 12L13 6" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="12.2422" y="2.34326" width="2" height="14" rx="1" transform="rotate(45 12.2422 2.34326)" fill="#07273D"/>
                    <rect x="13.6572" y="12.2427" width="2" height="14" rx="1" transform="rotate(135 13.6572 12.2427)" fill="#07273D"/>
                  </svg>
                </i>
              </div>
              <div id="collapse-faq3" class="collapse" aria-labelledby="heading-faq3" data-parent="#accordion-faq">
                Built-in masking in Filmora is perfect for beginners with easy-to-use tools including basic shapes, pen tool, AI detection, and parameter adjustments for edge blur, opacity, and positioning. Mocha masking offers professional-grade precision with planar tracking, ML smart segmentation, spline curves, and advanced export options including render shapes and alpha channels.
              </div>
            </div>
            <div class="faq-option">
              <div id="heading-faq4" class="faq-item d-flex justify-content-between with-hand font-weight-extra-bold collapsed" data-toggle="collapse" data-target="#collapse-faq4" aria-expanded="false" aria-controls="collapse-faq3" role="tab">
                Can I use masking in filmora for commercial projects?
                <i class="wsc-icon h-auto ml-3">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 6L8 12L13 6" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="12.2422" y="2.34326" width="2" height="14" rx="1" transform="rotate(45 12.2422 2.34326)" fill="#07273D"/>
                    <rect x="13.6572" y="12.2427" width="2" height="14" rx="1" transform="rotate(135 13.6572 12.2427)" fill="#07273D"/>
                  </svg>
                </i>
              </div>
              <div id="collapse-faq4" class="collapse" aria-labelledby="heading-faq3" data-parent="#accordion-faq">
                Yes, with a paid Filmora license, you can use all masking features including AI tools and Mocha integration for commercial projects without watermarks. The professional Mocha integration provides industry-standard capabilities suitable for commercial video production, advertising, and professional content creation.
              </div>
            </div>
            <div class="faq-option">
              <div id="heading-faq5" class="faq-item d-flex justify-content-between with-hand font-weight-extra-bold collapsed" data-toggle="collapse" data-target="#collapse-faq5" aria-expanded="false" aria-controls="collapse-faq5" role="tab">
                What are the advanced parameters I can adjust in Filmora masking?
                <i class="wsc-icon h-auto ml-3">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 6L8 12L13 6" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="12.2422" y="2.34326" width="2" height="14" rx="1" transform="rotate(45 12.2422 2.34326)" fill="#07273D"/>
                    <rect x="13.6572" y="12.2427" width="2" height="14" rx="1" transform="rotate(135 13.6572 12.2427)" fill="#07273D"/>
                  </svg>
                </i>
              </div>
              <div id="collapse-faq5" class="collapse" aria-labelledby="heading-faq5" data-parent="#accordion-faq">
                Wondershare Filmora masking offers extensive parameter control including edge blur intensity (0-100%), feather softness, brush size and hardness, corner radius, transparency levels, mask size scaling, width/height proportions, shape rotation (0-360°), X/Y positioning, mask inversion, and keyframe animation support for dynamic effects.
              </div>
            </div>
            <div class="faq-option">
              <div id="heading-faq6" class="faq-item d-flex justify-content-between with-hand font-weight-extra-bold collapsed" data-toggle="collapse" data-target="#collapse-faq6" aria-expanded="false" aria-controls="collapse-faq3" role="tab">
                How does Mocha integration work with Filmora?
                <i class="wsc-icon h-auto ml-3">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 6L8 12L13 6" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="12.2422" y="2.34326" width="2" height="14" rx="1" transform="rotate(45 12.2422 2.34326)" fill="#07273D"/>
                    <rect x="13.6572" y="12.2427" width="2" height="14" rx="1" transform="rotate(135 13.6572 12.2427)" fill="#07273D"/>
                  </svg>
                </i>
              </div>
              <div id="collapse-faq6" class="collapse" aria-labelledby="heading-faq6" data-parent="#accordion-faq">
                Mocha integration in Filmora provides professional VFX capabilities including Planar Tracker for perspective-aware tracking, Object Brush (ML) for neural network segmentation, and Matte Assist (ML) for intelligent cleanup. You can export spline paths, render shapes by color/layer ID/grayscale, and handle complex scenarios like occlusion tracking and foreground removal.
              </div>
            </div>
          </div>
					<div class="show-more">
						<span class="more">View All</span>
						<span class="less">Show Less</span> 
						<svg class="ml-2" width="16" style="height: 1rem;" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M15 1.5L8.70711 7.79289C8.31658 8.18342 7.68342 8.18342 7.29289 7.79289L1 1.5" stroke="black" stroke-width="2" stroke-linecap="round"/>
							<path d="M15 7.5L8.70711 13.7929C8.31658 14.1834 7.68342 14.1834 7.29289 13.7929L1 7.5" stroke="black" stroke-width="2" stroke-linecap="round"/>
							</svg>                        
					</div>
        </div>
      </div>
    </div>
  </section>
  <section class="py-md-5 py-4 mx-30 rounded-20" style="background-color: #9AFCE1;">
    <div class="container py-3 text-center">
      <h2 class="font-size-44 mb-md-3 text-black">What Our Customers Say About Us</h2>
      <div class="about-swiper swiper-container py-5">
        <div class="swiper-wrapper"> 
          <div class="swiper-slide h-auto">
            <div class="row justify-content-center">
              <div class="col-xl-8 col-md-10">
                <div class="d-flex justify-content-center align-items-center">
                  <div><img src="https://images.wondershare.com/filmora/features/lens-correction/customer-pic1.png" style="width: 3rem;border-radius: 50rem;" loading="lazy" alt="customer fred"></div>
                  <div class="text-left pl-2" style="letter-spacing: -0.02em;">
                    <h5 class="mb-1">Sarah Johnson</h5>
                    <div class="font-size-small">Content Creator</div>
                  </div>
                </div>
                <div class="pt-4 font-size-large">The AI masking feature in Filmora has revolutionized my workflow. What used to take hours now takes minutes! The edge detection is incredibly accurate.</div>
              </div>
            </div>
          </div>
          <div class="swiper-slide h-auto">
            <div class="row justify-content-center">
              <div class="col-xl-8 col-md-10">
                <div class="d-flex justify-content-center align-items-center">
                  <div><img src="https://images.wondershare.com/filmora/features/lens-correction/customer-pic2.png" style="width: 3rem;border-radius: 50rem;" loading="lazy" alt="customer david"></div>
                  <div class="text-left pl-2" style="letter-spacing: -0.02em;">
                    <h5 class="mb-1">Mike Chen</h5>
                    <div class="font-size-small">Commercial Video Production</div>
                  </div>
                </div>
                <div class="pt-4 font-size-large">Mocha integration is a game-changer. Professional tracking capabilities right inside Filmora - incredible! The planar tracking saved my commercial project.</div>
              </div>
            </div>
          </div>
          <div class="swiper-slide h-auto">
            <div class="row justify-content-center">
              <div class="col-xl-8 col-md-10">
                <div class="d-flex justify-content-center align-items-center">
                  <div><img src="https://images.wondershare.com/filmora/features/lens-correction/customer-pic3.png" style="width: 3rem;border-radius: 50rem;" loading="lazy" alt="customer megan"></div>
                  <div class="text-left pl-2" style="letter-spacing: -0.02em;">
                    <h5 class="mb-1">Emily Rodriguez</h5>
                    <div class="font-size-small">Educational Content Creation</div>
                  </div>
                </div>
                <div class="pt-4 font-size-large">Perfect balance of simplicity and power. The mask filmora tools are intuitive yet incredibly capable. Love the real-time preview feature!</div>
              </div>
            </div>
          </div>
        </div>
        <div class="swiper-pagination my-pagination"></div>
      </div>
    </div>
  </section>
  <div class="py-3"></div>
  <section class="part-ai my-5 py-5 mx-30 rounded-20" style="background-color: #F0FFF9;">
    <div class="container py-lg-4 text-center">
      <h2 class="font-size-44 mb-md-4 pt-3 text-black">Discover More Powerful Video Editing Features</h2>
      <div class="row row-cols-md-3 row-cols-1 pt-md-4">
        <div class="col py-3">
          <a href="https://filmora.wondershare.com/motion-tracking.html" class="box-style rounded-16">
            <img src="https://images.wondershare.com/filmora/features/video-mask/motion-tracking.png" class="img-fluid" loading="lazy" alt="Motion Tracking">
            <div class="box-title"><h4 class="mb-0">Motion Tracking</h4></div>
          </a>
        </div>
        <div class="col py-3">
          <a href="https://filmora.wondershare.com/color-match.html" class="box-style rounded-16">
            <img src="https://images.wondershare.com/filmora/features/planar-tracker/color-match-pic.png" class="img-fluid" loading="lazy" alt="Color Match">
            <div class="box-title"><h4 class="mb-0">Color Match</h4></div>
          </a>
        </div>
        <div class="col py-3">
          <a href="https://filmora.wondershare.com/keyframing.html" class="box-style rounded-16">
            <img src="https://images.wondershare.com/filmora/features/planar-tracker/keyframing-pic.png" class="img-fluid" loading="lazy" alt="Keyframing">
            <div class="box-title"><h4 class="mb-0">Keyframing</h4></div>
          </a>
        </div>
      </div>
      <div class="pt-3"><a href="https://filmora.wondershare.com/filmora-features.html" target="_blank" class="font-size-large font-weight-bold"><u>Check all features in Filmora >></u></a></div>
    </div>
  </section>
  <div class="py-md-3"></div>
  <div class="part-bottom py-xl-5">
     <div class="container text-md-left text-center py-3">
      <div class="row align-items-center pt-md-0 pt-4">
        <div class="col-md-6 pt-4 pb-md-4">
          <div class="pl-xxl-5">
            <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/filmora-horizontal-white.svg" style="height: 4rem;" loading="lazy" alt="filmora logo">
            <h2 class="pt-2 my-4 font-size-44">A video editor for all creators.</h2>
            <div class="sys-win dev-desktop">
              <div class="row no-gutters justify-content-center justify-content-md-start">
                <a href="https://download.wondershare.com/filmora_full846_guide-imagetovideo.exe" class="btn btn-lg btn-linear d-inline-flex align-items-center m-0 mr-3">
                  <i class="wsc-icon d-inline-flex align-items-center mr-2" data-icon="brand-windows"></i>
                Make Videos Now
                </a>
                <a href="https://filmora.wondershare.com/shop/buy/buy-video-editor.html" class="btn btn-lg btn-action m-0">Buy Now</a>
              </div>
            </div>
            <div class="sys-mac dev-desktop">
              <div class="row no-gutters justify-content-center justify-content-md-start">
                <a href="https://download.wondershare.com/filmora-mac_full14792_guide-imagetovideo.dmg" class="btn btn-lg btn-linear d-inline-flex align-items-center m-0 mr-3">
                  <i class="wsc-icon d-inline-flex align-items-center mr-2" data-icon="brand-macos"></i>
                 Make Videos Now
                </a>
                <a href="https://filmora.wondershare.com/shop/buy/buy-mac-video-editor.html" class="btn btn-lg btn-action m-0">Buy Now</a>
              </div>
            </div>
            <div class="dev-mobile">
              <div class="row no-gutters justify-content-center justify-content-md-start">
                <a href="https://filmora.go.link/6zpbQ" class="btn btn-lg btn-action m-0 mr-3">Make Videos Now</a>
                <a href="https://filmora.wondershare.com/shop/buy/buy-video-editor.html" class="btn btn-lg btn-white m-0">Buy Now</a>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 text-center"><img src="https://images.wondershare.com/filmora/features/portrait-cutout/bottom-right-pic.png" width="582" class="img-fluid mr-md-0 mr-n3" loading="lazy" alt="bottom-right-pic"></div>
      </div>
     </div>
  </div>
</main>
<%= require('html-loader!./commonHTML/footer.html') %>
<script src="https://neveragain.allstatics.com/2019/assets/vendor/wsc-vendor.js"></script>
<script src="https://neveragain.allstatics.com/2019/assets/script/wsc-override-fm.js"></script>
<script src="https://neveragain.allstatics.com/2019/assets/script/wsc-common.js"></script>
<script>$(function () { wsc.common.init() })</script>

<script src="https://neveragain.allstatics.com/2019/assets/vendor/swiper.min.js"></script>
<script>
  $(function(){
    if (document.body.clientWidth > 1280){
      var leave = false

      window.addEventListener('scroll', function() {
          var scrollElement = document.scrollingElement || document.documentElement || document.body
          var scrollTopStart = scrollElement.scrollTop + $(window).height() / 2
          var scrollTopEnd = scrollElement.scrollTop + $(window).height()
          var $downloadBlock1 = $("main section").eq(1)

          if((scrollTopStart > $downloadBlock1.offset().top && scrollTopEnd <= $downloadBlock1.offset().top + $downloadBlock1.height())) {
          $(".float-download-test").addClass("show")
          leave = true

          } else {
              if(leave) {
                  $(".float-download-test").addClass("leave")
                  setTimeout(function(){
                      $(".float-download-test").removeClass("leave")
                      $(".float-download-test").removeClass("show")
                  }, 1500)
                  leave = false
              }
              
          }
      })
      }
  })
</script>
<script>
(function(){
  // 视频滚动加载
  var lazy_videos = []
    document.querySelectorAll('.lazy-video').forEach(function(el){
      lazy_videos.push(el)
    })
    window.addEventListener('scroll', function() {
      lazy_videos && lazy_videos.forEach(function(el, index) {
        var top = el.getBoundingClientRect().top
        if (top <= window.innerHeight) {
          el.src = el.getAttribute('data-src')
          el.setAttribute('webkit-playsinline', '')
          el.setAttribute('playsinline', 'true')
          lazy_videos.splice(index, 1)
          lazy_videos = lazy_videos.length === 0 ? null : lazy_videos
        }
      })
    })
})()
</script> 
<script>
$(function () {
  
  var videoDom = document.getElementById('videoHero')
  if (videoDom) {
    videoDom.addEventListener('play', function () {
      $('#videoPlay').fadeOut('fast')
    })

    videoDom.addEventListener('pause', function () {
      $('#videoPlay').fadeIn('fast')
    })

    videoDom.addEventListener('ended', function () {
      $('#videoPlay').fadeIn('fast')
      videoDom.currentTime = 0
    })

    $('#modal-video').on('shown.bs.modal', function (event) {
      videoDom.src = $('[data-video]').attr('data-video')
      videoDom.poster = $('[data-video]').attr('data-poster')
      videoDom.play()

      $('#videoPlay').click(function () {
        videoDom.play()
      })
    })

    $('#modal-video').on('hidden.bs.modal', function (event) {
      videoDom.pause()
      videoDom.currentTime = 0
    })
  }

  $('body').on('wsc.event.youtube.ready', function () {
    var download_url = {
      win: 'https://download.wondershare.com/filmora_full846.exe',
      mac: 'https://download.wondershare.com/filmora-mac_full14792.dmg'
    }
    var modalDom =
      '<a href="' + download_url.win + '" class="btn btn-white d-inline-flex align-items-center m-0 mx-2"><i class="wsc-icon wsc-icon-sm mr-2"><svg class="wsc-svg-brand-windows" width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 6.61071L19.6714 3.9V22.9071H0V6.61071ZM0 41.3893L19.6714 44.1V25.3286H0V41.3893V41.3893ZM21.8357 44.3893L48 48V25.3286H21.8357V44.3893V44.3893ZM21.8357 3.61071V22.9071H48V0L21.8357 3.61071V3.61071Z" fill="currentColor"></path></svg></i> Try It Free</a><a href="' + download_url.mac + '" class="btn btn-white d-inline-flex align-items-center m-0 mx-2"><i class="wsc-icon wsc-icon-sm mr-2"><svg class="wsc-svg-brand-macos" width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M26.4185 15.9244V15.5289L25.189 15.6133C24.8409 15.6353 24.5795 15.7093 24.4037 15.8342C24.2279 15.9603 24.14 16.1349 24.14 16.3582C24.14 16.5756 24.2267 16.7491 24.4013 16.8775C24.5748 17.007 24.8085 17.0706 25.1 17.0706C25.2862 17.0706 25.4608 17.0417 25.6216 16.9851C25.7824 16.9284 25.9235 16.8486 26.0414 16.7468C26.1594 16.6462 26.252 16.5259 26.3179 16.3859C26.385 16.246 26.4185 16.0921 26.4185 15.9244ZM24 0C10.6074 0 0 10.6074 0 24C0 37.3937 10.6074 48 24 48C37.3926 48 48 37.3937 48 24C48 10.6074 37.3937 0 24 0ZM28.6589 14.2253C28.7722 13.9073 28.9319 13.6343 29.1389 13.4076C29.3459 13.1809 29.5946 13.0063 29.8861 12.8837C30.1775 12.7611 30.5026 12.6998 30.86 12.6998C31.1826 12.6998 31.4741 12.7483 31.7332 12.8443C31.9934 12.9403 32.2155 13.0699 32.4017 13.233C32.588 13.396 32.736 13.5857 32.8447 13.8032C32.9534 14.0206 33.0182 14.2508 33.0402 14.4925H32.0767C32.0524 14.3595 32.0085 14.2346 31.946 14.1201C31.8836 14.0056 31.8014 13.9061 31.6997 13.8217C31.5967 13.7373 31.4764 13.6713 31.3388 13.6239C31.2 13.5753 31.045 13.5522 30.8704 13.5522C30.6656 13.5522 30.4794 13.5938 30.314 13.676C30.1475 13.7581 30.0052 13.8749 29.8872 14.0253C29.7693 14.1756 29.6779 14.3595 29.6131 14.5746C29.5472 14.7909 29.5148 15.0304 29.5148 15.2941C29.5148 15.567 29.5472 15.8122 29.6131 16.0274C29.6779 16.2437 29.7704 16.4253 29.8907 16.5745C30.0098 16.7237 30.1544 16.8382 30.3221 16.9168C30.4898 16.9955 30.6749 17.036 30.8761 17.036C31.2058 17.036 31.4741 16.9585 31.6823 16.8035C31.8905 16.6485 32.0247 16.4218 32.0871 16.1234H33.0518C33.024 16.3871 32.9511 16.6277 32.8332 16.8451C32.7152 17.0626 32.5625 17.2476 32.3751 17.4014C32.1866 17.5553 31.9657 17.6744 31.7112 17.7577C31.4568 17.841 31.1769 17.8838 30.8727 17.8838C30.5118 17.8838 30.1868 17.8236 29.8942 17.7045C29.6027 17.5853 29.3517 17.413 29.1435 17.1898C28.9353 16.9666 28.7746 16.6947 28.6612 16.3744C28.5479 16.054 28.4912 15.6943 28.4912 15.2929C28.4889 14.9008 28.5455 14.5446 28.6589 14.2253V14.2253ZM14.9494 12.7657H15.914V13.6227H15.9325C15.9915 13.4805 16.069 13.3521 16.1639 13.2411C16.2587 13.1289 16.3674 13.034 16.4923 12.9542C16.6161 12.8744 16.7537 12.8143 16.9018 12.7726C17.051 12.731 17.2083 12.7102 17.3725 12.7102C17.7265 12.7102 18.026 12.7946 18.2689 12.9635C18.513 13.1323 18.6865 13.3752 18.7882 13.6921H18.8125C18.8773 13.5395 18.9629 13.403 19.0681 13.2827C19.1734 13.1624 19.2948 13.0583 19.4313 12.9727C19.5678 12.8871 19.7182 12.8224 19.8813 12.7773C20.0443 12.7321 20.2155 12.7102 20.396 12.7102C20.6446 12.7102 20.8702 12.7495 21.0737 12.8293C21.2773 12.9091 21.4508 13.0201 21.5954 13.1647C21.74 13.3093 21.851 13.4851 21.9285 13.691C22.006 13.8969 22.0453 14.1259 22.0453 14.378V17.8259H21.039V14.6198C21.039 14.2878 20.9534 14.0299 20.7823 13.8483C20.6122 13.6667 20.3682 13.5753 20.0513 13.5753C19.8963 13.5753 19.754 13.6031 19.6245 13.6574C19.4961 13.7118 19.3839 13.7881 19.2914 13.8865C19.1977 13.9836 19.1248 14.1016 19.0728 14.2381C19.0196 14.3746 18.993 14.5238 18.993 14.6857V17.8259H18.0006V14.5365C18.0006 14.3907 17.9774 14.2589 17.9323 14.1409C17.8872 14.0229 17.8236 13.9223 17.7392 13.8379C17.6559 13.7534 17.553 13.6898 17.4338 13.6447C17.3135 13.5996 17.1794 13.5765 17.0302 13.5765C16.8752 13.5765 16.7318 13.6054 16.5987 13.6632C16.4669 13.7211 16.3535 13.8009 16.2587 13.9027C16.1639 14.0056 16.0898 14.1259 16.0378 14.2658C15.9869 14.4046 15.9036 14.5573 15.9036 14.7215V17.8248H14.9494V12.7657V12.7657ZM17.1389 36.9843C12.7148 36.9843 9.94005 33.91 9.94005 29.0036C9.94005 24.0972 12.7148 21.0124 17.1389 21.0124C21.563 21.0124 24.3273 24.0972 24.3273 29.0036C24.3273 33.9088 21.563 36.9843 17.1389 36.9843V36.9843ZM25.3301 17.8132C25.167 17.8548 25.0005 17.8757 24.8293 17.8757C24.5772 17.8757 24.347 17.8398 24.1376 17.7681C23.9271 17.6964 23.7479 17.5958 23.5975 17.4651C23.4471 17.3344 23.3292 17.1771 23.2459 16.992C23.1614 16.8069 23.1198 16.6011 23.1198 16.3744C23.1198 15.9302 23.2852 15.5832 23.616 15.3334C23.9468 15.0836 24.4256 14.9378 25.0537 14.8973L26.4185 14.8187V14.4278C26.4185 14.1363 26.326 13.9131 26.1409 13.7615C25.9559 13.61 25.6945 13.5337 25.3556 13.5337C25.2191 13.5337 25.0907 13.551 24.9727 13.5846C24.8547 13.6193 24.7506 13.6679 24.6604 13.7315C24.5702 13.7951 24.495 13.8714 24.4372 13.9605C24.3782 14.0484 24.3377 14.1479 24.3158 14.2566H23.3696C23.3754 14.0333 23.4309 13.8263 23.535 13.6366C23.6391 13.4469 23.7802 13.2827 23.9595 13.1427C24.1388 13.0028 24.3481 12.8941 24.591 12.8166C24.8339 12.7391 25.0976 12.6998 25.3833 12.6998C25.691 12.6998 25.9697 12.7379 26.2196 12.8166C26.4694 12.8952 26.6834 13.0051 26.8603 13.1497C27.0373 13.2943 27.1738 13.4678 27.2698 13.6713C27.3658 13.8749 27.4144 14.1027 27.4144 14.3537V17.8248H26.4509V16.9816H26.4266C26.3549 17.1181 26.2647 17.2418 26.1548 17.3517C26.0438 17.4616 25.92 17.5564 25.7824 17.6339C25.6436 17.7114 25.4932 17.7716 25.3301 17.8132V17.8132ZM31.695 36.9843C28.3212 36.9843 26.0276 35.217 25.868 32.4422H28.0644C28.2356 34.028 29.7681 35.0782 31.8674 35.0782C33.8811 35.0782 35.328 34.028 35.328 32.5926C35.328 31.3504 34.449 30.5997 32.4145 30.0862L30.432 29.5934C27.5821 28.8867 26.2866 27.5902 26.2866 25.4585C26.2866 22.8341 28.5791 21.0124 31.8466 21.0124C35.0388 21.0124 37.2665 22.8445 37.3521 25.4793H35.1776C35.0273 23.8936 33.7319 22.9186 31.8026 22.9186C29.8849 22.9186 28.5571 23.904 28.5571 25.329C28.5571 26.4532 29.3922 27.1183 31.4279 27.6318L33.0992 28.0493C36.2811 28.8208 37.588 30.0746 37.588 32.3242C37.5869 35.195 35.3164 36.9843 31.695 36.9843V36.9843ZM17.1389 22.9822C14.1074 22.9822 12.2117 25.307 12.2117 29.0024C12.2117 32.6874 14.1074 35.0122 17.1389 35.0122C20.16 35.0122 22.0661 32.6874 22.0661 29.0024C22.0673 25.307 20.16 22.9822 17.1389 22.9822V22.9822Z" fill="currentColor"></path></svg></i> Try It Free</a>'
    $('#modal-youtube .modal-footer button[data-dismiss="modal"]').remove()
    $('#modal-youtube .modal-footer').prepend(modalDom)
    $('#modal-youtube .modal-footer').addClass('justify-content-center')
  })
  
  if (document.querySelectorAll('.effects-swiper').length > 0) (
    new Swiper('.effects-swiper', {
      speed: 5000,
      loop: true,
      slidesPerView: 'auto',
      spaceBetween: 30,
      noSwiping: true,
      autoplay: {
        delay: 0,
        disableOnInteraction: false,
      },
      breakpoints: {
        992: {
          spaceBetween: 20,
        },
        576: {
          spaceBetween: 15,
        }
      }
    })
  )

  new Swiper('.features-swiper', {
    slidesPerView: 4,
    slidesPerGroup: 4,
    slidesPerColumn: 2,
    spaceBetween: 30,
    navigation: {
      prevEl: '.features-prev',
      nextEl: '.features-next',
    },
    breakpoints: {
      576: {
        slidesPerView: 'auto',
        slidesPerGroup: 1,
        slidesPerColumn: 1,
        spaceBetween: 15,
      }
    }
  })

  new Swiper('.content-swiper', {
    noSwiping : true,
    noSwipingClass : 'no-swiping',
    speed: 100,
    autoplay: {
      delay: 1000
    },
    effect: 'fade',
    fadeEffect: {
      crossFade: false,
    },
  })
})
</script>

<script src="https://neveragain.allstatics.com/2019/assets/nps/nps.js" defer ></script>
<!-- 新样式脚本 -->
<script>
  	$(".part-faq").find('.faq-option:gt(4)').hide()
    $('.part-faq .show-more').on('click',function(){
      $(this).toggleClass('show')
      $(".part-faq").find('.faq-option:gt(4)').slideToggle(300)
    })
	if ($('.about-swiper').length > 0) {
		new Swiper('.about-swiper', {
      slidesPerView: 1,
      spaceBetween: 30,
			loop: true,
			autoplay: {
				delay: 4000,
				disableOnInteraction: false,
			},
			pagination: {
				el: '.about-swiper .my-pagination',
				clickable :true,
			},
    })
	}

  // 点击swiper切换方法
  function checkSwiper(swiper,thumbsAll,index){
      thumbsAll.removeClass('active')
      thumbsAll.eq(index).addClass('active')
      swiper.slideTo(index, 500, false);
    }

  function stepSwiper(el,thumbsAll){
    return new Swiper(el, {
      slidesPerView: 1,
      observer: true,  //开启动态检查器，监测swiper和slide
      observeParents: true,  //监测Swiper 的祖/父元素
      autoplay: {
        delay: 5000,
        disableOnInteraction: false
      },
      spaceBetween: 30,
      pagination: {
        el: el + ' .swiper-pagination',
        clickable: true
      },
      on: {
        slideChange: function () {
          thumbsAll.eq(this.activeIndex).addClass('active').siblings().removeClass('active')
        }
      }
    })
  }

  function createCommonSwiper(elm,thumbsArray){
    var swiper_inited = false
    document.querySelectorAll(elm + ' video').forEach(function(el) {
      el.removeAttribute('autoplay')
    })
    
    return new Swiper(elm, {
      slidesPerView: 1,
      spaceBetween: 30,
      on: {
        init() {
          var swiper = this
          swiper_inited = true
          $(elm + ' video').each(function(index,el) {
            el.addEventListener('ended', function() {
              swiper.isEnd ? swiper.slideTo(0) : swiper.slideNext()
            })
          })
          $(elm + ' video')[this.activeIndex].addEventListener('canplay', function(event) {
            $(this)[0].play()
          })
        },
        slideChange: function(){
          swiper_inited && ($(elm + ' video')[this.activeIndex].play(), $(elm + ' video')[this.previousIndex].pause())
        },
        slideChangeTransitionStart: function() {
          thumbsArray.removeClass('active')
          thumbsArray.eq(this.activeIndex).addClass('active')
        }
      },
    })
  }

	if(window.innerWidth >= 1280) {
    // 步骤版块脚本
    if($('.stepSwiper1').length) {
      var stepSwiper1 = stepSwiper('.stepSwiper1', $('.step1-list li'))
      $('.step1-list li').click(function(){
        checkSwiper(stepSwiper1,$('.step1-list li'),$(this).index())
      })
    }
    if($('.stepSwiper2').length) {
      var stepSwiper2 = stepSwiper('.stepSwiper2', $('.step2-list li'))
      $('.step2-list li').click(function(){
        checkSwiper(stepSwiper2,$('.step2-list li'),$(this).index())
      })
    }
    

	} else {

  }
</script>
</body>

</html>